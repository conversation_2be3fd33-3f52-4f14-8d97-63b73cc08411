{"version": 3, "sources": ["../../../src/shared/lib/deep-freeze.ts"], "sourcesContent": ["import type { DeepReadonly } from './deep-readonly'\n\n/**\n * Recursively freezes an object and all of its properties. This prevents the\n * object from being modified at runtime. When the JS runtime is running in\n * strict mode, any attempts to modify a frozen object will throw an error.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n * @param obj The object to freeze.\n */\nexport function deepFreeze<T extends object>(obj: T): DeepReadonly<T> {\n  // If the object is already frozen, there's no need to freeze it again.\n  if (Object.isFrozen(obj)) return obj as DeepReadonly<T>\n\n  // An array is an object, but we also want to freeze each element in the array\n  // as well.\n  if (Array.isArray(obj)) {\n    for (const item of obj) {\n      if (!item || typeof item !== 'object') continue\n      deepFreeze(item)\n    }\n\n    return Object.freeze(obj) as DeepReadonly<T>\n  }\n\n  for (const value of Object.values(obj)) {\n    if (!value || typeof value !== 'object') continue\n    deepFreeze(value)\n  }\n\n  return Object.freeze(obj) as Deep<PERSON><PERSON>only<T>\n}\n"], "names": ["deepFreeze", "obj", "Object", "isFrozen", "Array", "isArray", "item", "freeze", "value", "values"], "mappings": "AAEA;;;;;;;CAOC,GACD,OAAO,SAASA,WAA6BC,GAAM;IACjD,uEAAuE;IACvE,IAAIC,OAAOC,QAAQ,CAACF,MAAM,OAAOA;IAEjC,8EAA8E;IAC9E,WAAW;IACX,IAAIG,MAAMC,OAAO,CAACJ,MAAM;QACtB,KAAK,MAAMK,QAAQL,IAAK;YACtB,IAAI,CAACK,QAAQ,OAAOA,SAAS,UAAU;YACvCN,WAAWM;QACb;QAEA,OAAOJ,OAAOK,MAAM,CAACN;IACvB;IAEA,KAAK,MAAMO,SAASN,OAAOO,MAAM,CAACR,KAAM;QACtC,IAAI,CAACO,SAAS,OAAOA,UAAU,UAAU;QACzCR,WAAWQ;IACb;IAEA,OAAON,OAAOK,MAAM,CAACN;AACvB", "ignoreList": [0]}