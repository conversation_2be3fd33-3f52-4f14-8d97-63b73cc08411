'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  children: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', loading = false, children, disabled, ...props }, ref) => {
    const baseClasses = cn(
      // 基础样式
      'inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-apple-blue',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'active:scale-[0.98] transform',
      
      // 尺寸变体
      {
        'px-3 py-2 text-sm h-9': size === 'sm',
        'px-4 py-2.5 text-base h-11': size === 'md',
        'px-6 py-3 text-lg h-12': size === 'lg',
      },
      
      // 颜色变体
      {
        // Primary - 苹果蓝色
        'bg-apple-blue text-white hover:bg-blue-600 shadow-apple': variant === 'primary',
        
        // Secondary - 灰色
        'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700': variant === 'secondary',
        
        // Outline - 边框样式
        'border-2 border-apple-blue text-apple-blue hover:bg-apple-blue hover:text-white': variant === 'outline',
        
        // Ghost - 透明背景
        'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800': variant === 'ghost',
        
        // Destructive - 红色警告
        'bg-apple-red text-white hover:bg-red-600 shadow-apple': variant === 'destructive',
      },
      
      className
    )

    return (
      <motion.button
        ref={ref}
        className={baseClasses}
        disabled={disabled || loading}
        whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
        whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
        {...(props as any)}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </motion.button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
