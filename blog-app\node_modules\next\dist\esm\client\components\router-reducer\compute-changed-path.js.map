{"version": 3, "sources": ["../../../../src/client/components/router-reducer/compute-changed-path.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "isGroupSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "matchSegment", "removeLeadingSlash", "segment", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "extractPathFromFlightRouterState", "flightRouterState", "Array", "isArray", "some", "m", "startsWith", "undefined", "parallelRoutes", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "Object", "entries", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "computeChangedPath", "split", "getSelectedParams", "currentTree", "params", "parallelRoute", "values", "isDynamicParameter", "segmentValue", "isCatchAll"], "mappings": "AAIA,SAASA,0BAA0B,QAAQ,uDAAsD;AAEjG,SACEC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,8BAA6B;AACpC,SAASC,YAAY,QAAQ,oBAAmB;AAEhD,MAAMC,qBAAqB,CAACC;IAC1B,OAAOA,OAAO,CAAC,EAAE,KAAK,MAAMA,QAAQC,KAAK,CAAC,KAAKD;AACjD;AAEA,MAAME,oBAAoB,CAACF;IACzB,IAAI,OAAOA,YAAY,UAAU;QAC/B,uHAAuH;QACvH,gHAAgH;QAChH,IAAIA,YAAY,YAAY,OAAO;QAEnC,OAAOA;IACT;IAEA,OAAOA,OAAO,CAAC,EAAE;AACnB;AAEA,SAASG,kBAAkBC,QAAkB;IAC3C,OACEA,SAASC,MAAM,CAAC,CAACC,KAAKN;QACpBA,UAAUD,mBAAmBC;QAC7B,IAAIA,YAAY,MAAML,eAAeK,UAAU;YAC7C,OAAOM;QACT;QAEA,OAAO,AAAGA,MAAI,MAAGN;IACnB,GAAG,OAAO;AAEd;AAEA,OAAO,SAASO,iCACdC,iBAAoC;IAEpC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,iBAAiB,CAAC,EAAE,IAC9CA,iBAAiB,CAAC,EAAE,CAAC,EAAE,GACvBA,iBAAiB,CAAC,EAAE;IAExB,IACER,YAAYJ,uBACZF,2BAA2BiB,IAAI,CAAC,CAACC,IAAMZ,QAAQa,UAAU,CAACD,KAE1D,OAAOE;IAET,IAAId,QAAQa,UAAU,CAAChB,mBAAmB,OAAO;IAEjD,MAAMO,WAAW;QAACF,kBAAkBF;KAAS;QACtBQ;IAAvB,MAAMO,iBAAiBP,CAAAA,sBAAAA,iBAAiB,CAAC,EAAE,YAApBA,sBAAwB,CAAC;IAEhD,MAAMQ,eAAeD,eAAeE,QAAQ,GACxCV,iCAAiCQ,eAAeE,QAAQ,IACxDH;IAEJ,IAAIE,iBAAiBF,WAAW;QAC9BV,SAASc,IAAI,CAACF;IAChB,OAAO;QACL,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACP,gBAAiB;YACzD,IAAII,QAAQ,YAAY;YAExB,MAAMI,YAAYhB,iCAAiCa;YAEnD,IAAIG,cAAcT,WAAW;gBAC3BV,SAASc,IAAI,CAACK;YAChB;QACF;IACF;IAEA,OAAOpB,kBAAkBC;AAC3B;AAEA,SAASoB,uBACPC,KAAwB,EACxBC,KAAwB;IAExB,MAAM,CAACC,UAAUC,gBAAgB,GAAGH;IACpC,MAAM,CAACI,UAAUC,gBAAgB,GAAGJ;IAEpC,MAAMK,qBAAqB7B,kBAAkByB;IAC7C,MAAMK,qBAAqB9B,kBAAkB2B;IAE7C,IACEnC,2BAA2BiB,IAAI,CAC7B,CAACC,IACCmB,mBAAmBlB,UAAU,CAACD,MAAMoB,mBAAmBnB,UAAU,CAACD,KAEtE;QACA,OAAO;IACT;IAEA,IAAI,CAACd,aAAa6B,UAAUE,WAAW;YAE9BtB;QADP,8FAA8F;QAC9F,OAAOA,CAAAA,oCAAAA,iCAAiCmB,kBAAjCnB,oCAA2C;IACpD;IAEA,IAAK,MAAM0B,qBAAqBL,gBAAiB;QAC/C,IAAIE,eAAe,CAACG,kBAAkB,EAAE;YACtC,MAAMC,cAAcV,uBAClBI,eAAe,CAACK,kBAAkB,EAClCH,eAAe,CAACG,kBAAkB;YAEpC,IAAIC,gBAAgB,MAAM;gBACxB,OAAO,AAAGhC,kBAAkB2B,YAAU,MAAGK;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEA,OAAO,SAASC,mBACdV,KAAwB,EACxBC,KAAwB;IAExB,MAAMQ,cAAcV,uBAAuBC,OAAOC;IAElD,IAAIQ,eAAe,QAAQA,gBAAgB,KAAK;QAC9C,OAAOA;IACT;IAEA,mDAAmD;IACnD,OAAO/B,kBAAkB+B,YAAYE,KAAK,CAAC;AAC7C;AAEA;;CAEC,GACD,OAAO,SAASC,kBACdC,WAA8B,EAC9BC,MAAmB;IAAnBA,IAAAA,mBAAAA,SAAiB,CAAC;IAElB,MAAMxB,iBAAiBuB,WAAW,CAAC,EAAE;IAErC,KAAK,MAAME,iBAAiBnB,OAAOoB,MAAM,CAAC1B,gBAAiB;QACzD,MAAMf,UAAUwC,aAAa,CAAC,EAAE;QAChC,MAAME,qBAAqBjC,MAAMC,OAAO,CAACV;QACzC,MAAM2C,eAAeD,qBAAqB1C,OAAO,CAAC,EAAE,GAAGA;QACvD,IAAI,CAAC2C,gBAAgBA,aAAa9B,UAAU,CAAChB,mBAAmB;QAEhE,iEAAiE;QACjE,MAAM+C,aACJF,sBAAuB1C,CAAAA,OAAO,CAAC,EAAE,KAAK,OAAOA,OAAO,CAAC,EAAE,KAAK,IAAG;QAEjE,IAAI4C,YAAY;YACdL,MAAM,CAACvC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE,CAACoC,KAAK,CAAC;QACxC,OAAO,IAAIM,oBAAoB;YAC7BH,MAAM,CAACvC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE;QACjC;QAEAuC,SAASF,kBAAkBG,eAAeD;IAC5C;IAEA,OAAOA;AACT", "ignoreList": [0]}