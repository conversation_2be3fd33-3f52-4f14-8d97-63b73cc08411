{"version": 3, "sources": [], "sections": [{"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\n// 环境变量检查\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables')\n}\n\n// 客户端 Supabase 实例（用于客户端组件）\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// 服务端 Supabase 实例（用于服务端组件）\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // 在服务端组件中设置cookie可能会失败\n          // 这是正常的，因为服务端组件是只读的\n        }\n      },\n    },\n  })\n}\n\n// 路由处理器 Supabase 实例（用于API路由）\nexport const createRouteHandlerClient = (request: Request) => {\n  const cookieStore = request.headers.get('cookie') || ''\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore\n          .split(';')\n          .map(cookie => cookie.trim().split('='))\n          .filter(([name]) => name)\n          .map(([name, value]) => ({ name, value }))\n      },\n      setAll(cookiesToSet) {\n        // 在API路由中处理cookie设置\n        // 这里可以根据需要实现cookie设置逻辑\n      },\n    },\n  })\n}\n\n// 数据库类型定义\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          bio: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      posts: {\n        Row: {\n          id: string\n          title: string\n          content: string\n          excerpt: string | null\n          slug: string\n          published: boolean\n          author_id: string\n          category_id: string | null\n          featured_image: string | null\n          created_at: string\n          updated_at: string\n          published_at: string | null\n        }\n        Insert: {\n          id?: string\n          title: string\n          content: string\n          excerpt?: string | null\n          slug: string\n          published?: boolean\n          author_id: string\n          category_id?: string | null\n          featured_image?: string | null\n          created_at?: string\n          updated_at?: string\n          published_at?: string | null\n        }\n        Update: {\n          id?: string\n          title?: string\n          content?: string\n          excerpt?: string | null\n          slug?: string\n          published?: boolean\n          author_id?: string\n          category_id?: string | null\n          featured_image?: string | null\n          created_at?: string\n          updated_at?: string\n          published_at?: string | null\n        }\n      }\n      categories: {\n        Row: {\n          id: string\n          name: string\n          slug: string\n          description: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          slug: string\n          description?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          slug?: string\n          description?: string | null\n          created_at?: string\n        }\n      }\n      post_likes: {\n        Row: {\n          id: string\n          post_id: string\n          user_id: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          post_id: string\n          user_id: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          post_id?: string\n          user_id?: string\n          created_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n\n// 默认客户端实例（向后兼容）\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;AAEA,SAAS;AACT,MAAM;AACN,MAAM;AAEN;;AAKO,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,uRAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,sRAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,uBAAuB;gBACvB,oBAAoB;gBACtB;YACF;QACF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;IAErD,OAAO,CAAA,GAAA,sRAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI,GAAG,KAAK,CAAC,MAClC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAK,MACnB,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;wBAAE;wBAAM;oBAAM,CAAC;YAC5C;YACA,QAAO,YAAY;YACjB,oBAAoB;YACpB,uBAAuB;YACzB;QACF;IACF;AACF;AAwIO,MAAM,WAAW,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/lib/auth.ts"], "sourcesContent": ["import { createClientComponentClient } from './supabase'\nimport type { User } from '@supabase/supabase-js'\n\n// 认证相关工具函数\nexport class AuthService {\n  private supabase = createClientComponentClient()\n\n  // 用户注册\n  async signUp(email: string, password: string, fullName?: string) {\n    try {\n      const { data, error } = await this.supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n\n      if (error) throw error\n\n      return { user: data.user, error: null }\n    } catch (error) {\n      return { user: null, error: error as Error }\n    }\n  }\n\n  // 用户登录\n  async signIn(email: string, password: string) {\n    try {\n      const { data, error } = await this.supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n\n      return { user: data.user, error: null }\n    } catch (error) {\n      return { user: null, error: error as Error }\n    }\n  }\n\n  // 用户登出\n  async signOut() {\n    try {\n      const { error } = await this.supabase.auth.signOut()\n      if (error) throw error\n      return { error: null }\n    } catch (error) {\n      return { error: error as Error }\n    }\n  }\n\n  // 获取当前用户\n  async getCurrentUser(): Promise<User | null> {\n    try {\n      const { data: { user } } = await this.supabase.auth.getUser()\n      return user\n    } catch (error) {\n      console.error('获取用户信息失败:', error)\n      return null\n    }\n  }\n\n  // 重置密码\n  async resetPassword(email: string) {\n    try {\n      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n      return { error: null }\n    } catch (error) {\n      return { error: error as Error }\n    }\n  }\n\n  // 更新密码\n  async updatePassword(newPassword: string) {\n    try {\n      const { error } = await this.supabase.auth.updateUser({\n        password: newPassword,\n      })\n\n      if (error) throw error\n      return { error: null }\n    } catch (error) {\n      return { error: error as Error }\n    }\n  }\n\n  // 监听认证状态变化\n  onAuthStateChange(callback: (user: User | null) => void) {\n    return this.supabase.auth.onAuthStateChange((event, session) => {\n      callback(session?.user ?? null)\n    })\n  }\n\n  // 获取用户资料\n  async getUserProfile(userId: string) {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n      return { profile: data, error: null }\n    } catch (error) {\n      return { profile: null, error: error as Error }\n    }\n  }\n\n  // 更新用户资料\n  async updateUserProfile(userId: string, updates: {\n    full_name?: string\n    avatar_url?: string\n    bio?: string\n  }) {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .update({\n          ...updates,\n          updated_at: new Date().toISOString(),\n        })\n        .eq('id', userId)\n        .select()\n        .single()\n\n      if (error) throw error\n      return { profile: data, error: null }\n    } catch (error) {\n      return { profile: null, error: error as Error }\n    }\n  }\n}\n\n// 导出单例实例\nexport const authService = new AuthService()\n\n// 认证状态类型\nexport type AuthState = {\n  user: User | null\n  loading: boolean\n  error: string | null\n}\n\n// 认证错误处理\nexport const getAuthErrorMessage = (error: Error): string => {\n  const message = error.message.toLowerCase()\n  \n  if (message.includes('invalid login credentials')) {\n    return '邮箱或密码错误'\n  }\n  \n  if (message.includes('email not confirmed')) {\n    return '请先验证您的邮箱'\n  }\n  \n  if (message.includes('password should be at least')) {\n    return '密码至少需要6个字符'\n  }\n  \n  if (message.includes('user already registered')) {\n    return '该邮箱已被注册'\n  }\n  \n  if (message.includes('invalid email')) {\n    return '请输入有效的邮箱地址'\n  }\n  \n  return error.message || '发生未知错误'\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAIO,MAAM;IACH,WAAW,CAAA,GAAA,+GAAA,CAAA,8BAA2B,AAAD,IAAG;IAEhD,OAAO;IACP,MAAM,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAiB,EAAE;QAC/D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACtD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,MAAM,KAAK,IAAI;gBAAE,OAAO;YAAK;QACxC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAe;QAC7C;IACF;IAEA,OAAO;IACP,MAAM,OAAO,KAAa,EAAE,QAAgB,EAAE;QAC5C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAClE;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,MAAM,KAAK,IAAI;gBAAE,OAAO;YAAK;QACxC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAe;QAC7C;IACF;IAEA,OAAO;IACP,MAAM,UAAU;QACd,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,SAAS;IACT,MAAM,iBAAuC;QAC3C,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC3D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,cAAc,KAAa,EAAE;QACjC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACtE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,OAAO;IACP,MAAM,eAAe,WAAmB,EAAE;QACxC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;gBACpD,UAAU;YACZ;YAEA,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,WAAW;IACX,kBAAkB,QAAqC,EAAE;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO;YAClD,SAAS,SAAS,QAAQ;QAC5B;IACF;IAEA,SAAS;IACT,MAAM,eAAe,MAAc,EAAE;QACnC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAK;QACtC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAe;QAChD;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,MAAc,EAAE,OAIvC,EAAE;QACD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAK;QACtC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAe;QAChD;IACF;AACF;AAGO,MAAM,cAAc,IAAI;AAUxB,MAAM,sBAAsB,CAAC;IAClC,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW;IAEzC,IAAI,QAAQ,QAAQ,CAAC,8BAA8B;QACjD,OAAO;IACT;IAEA,IAAI,QAAQ,QAAQ,CAAC,wBAAwB;QAC3C,OAAO;IACT;IAEA,IAAI,QAAQ,QAAQ,CAAC,gCAAgC;QACnD,OAAO;IACT;IAEA,IAAI,QAAQ,QAAQ,CAAC,4BAA4B;QAC/C,OAAO;IACT;IAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;QACrC,OAAO;IACT;IAEA,OAAO,MAAM,OAAO,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { authService, type AuthState } from '@/lib/auth'\n\ninterface AuthContextType extends AuthState {\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: Error | null }>\n  signUp: (email: string, password: string, fullName?: string) => Promise<{ user: User | null; error: Error | null }>\n  signOut: () => Promise<{ error: Error | null }>\n  resetPassword: (email: string) => Promise<{ error: Error | null }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    loading: true,\n    error: null,\n  })\n\n  useEffect(() => {\n    // 获取初始用户状态\n    const getInitialUser = async () => {\n      try {\n        const user = await authService.getCurrentUser()\n        setAuthState(prev => ({\n          ...prev,\n          user,\n          loading: false,\n        }))\n      } catch (error) {\n        setAuthState(prev => ({\n          ...prev,\n          user: null,\n          loading: false,\n          error: error instanceof Error ? error.message : '获取用户信息失败',\n        }))\n      }\n    }\n\n    getInitialUser()\n\n    // 监听认证状态变化\n    const { data: { subscription } } = authService.onAuthStateChange((user) => {\n      setAuthState(prev => ({\n        ...prev,\n        user,\n        loading: false,\n        error: null,\n      }))\n    })\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const result = await authService.signIn(email, password)\n    \n    if (result.error) {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false,\n        error: result.error?.message || '登录失败',\n      }))\n    } else {\n      setAuthState(prev => ({\n        ...prev,\n        user: result.user,\n        loading: false,\n        error: null,\n      }))\n    }\n    \n    return result\n  }\n\n  const signUp = async (email: string, password: string, fullName?: string) => {\n    setAuthState(prev => ({ ...prev, loading: true, error: null }))\n    \n    const result = await authService.signUp(email, password, fullName)\n    \n    if (result.error) {\n      setAuthState(prev => ({\n        ...prev,\n        loading: false,\n        error: result.error?.message || '注册失败',\n      }))\n    } else {\n      setAuthState(prev => ({\n        ...prev,\n        user: result.user,\n        loading: false,\n        error: null,\n      }))\n    }\n    \n    return result\n  }\n\n  const signOut = async () => {\n    setAuthState(prev => ({ ...prev, loading: true }))\n    \n    const result = await authService.signOut()\n    \n    setAuthState(prev => ({\n      ...prev,\n      user: null,\n      loading: false,\n      error: result.error?.message || null,\n    }))\n    \n    return result\n  }\n\n  const resetPassword = async (email: string) => {\n    return await authService.resetPassword(email)\n  }\n\n  const value: AuthContextType = {\n    ...authState,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAaA,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,OAAO,MAAM,2GAAA,CAAA,cAAW,CAAC,cAAc;gBAC7C,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP;wBACA,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM;wBACN,SAAS;wBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD,CAAC;YACH;QACF;QAEA;QAEA,WAAW;QACX,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,2GAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC,CAAC;YAChE,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP;oBACA,SAAS;oBACT,OAAO;gBACT,CAAC;QACH;QAEA,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAE7D,MAAM,SAAS,MAAM,2GAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO;QAE/C,IAAI,OAAO,KAAK,EAAE;YAChB,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,OAAO,KAAK,EAAE,WAAW;gBAClC,CAAC;QACH,OAAO;YACL,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM,OAAO,IAAI;oBACjB,SAAS;oBACT,OAAO;gBACT,CAAC;QACH;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAE7D,MAAM,SAAS,MAAM,2GAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO,UAAU;QAEzD,IAAI,OAAO,KAAK,EAAE;YAChB,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,OAAO,KAAK,EAAE,WAAW;gBAClC,CAAC;QACH,OAAO;YACL,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM,OAAO,IAAI;oBACjB,SAAS;oBACT,OAAO;gBACT,CAAC;QACH;QAEA,OAAO;IACT;IAEA,MAAM,UAAU;QACd,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAK,CAAC;QAEhD,MAAM,SAAS,MAAM,2GAAA,CAAA,cAAW,CAAC,OAAO;QAExC,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO,OAAO,KAAK,EAAE,WAAW;YAClC,CAAC;QAED,OAAO;IACT;IAEA,MAAM,gBAAgB,OAAO;QAC3B,OAAO,MAAM,2GAAA,CAAA,cAAW,CAAC,aAAa,CAAC;IACzC;IAEA,MAAM,QAAyB;QAC7B,GAAG,SAAS;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,6WAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}