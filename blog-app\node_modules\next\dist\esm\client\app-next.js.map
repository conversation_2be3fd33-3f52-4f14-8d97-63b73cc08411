{"version": 3, "sources": ["../../src/client/app-next.ts"], "sourcesContent": ["// This import must go first because it needs to patch webpack chunk loading\n// before React patches chunk loading.\nimport './app-webpack'\nimport { appBootstrap } from './app-bootstrap'\n\nconst instrumentationHooks =\n  // eslint-disable-next-line @next/internal/typechecked-require -- not a module\n  require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  // Include app-router and layout-router in the main chunk\n  // eslint-disable-next-line @next/internal/typechecked-require -- Why not relative imports?\n  require('next/dist/client/components/app-router')\n  // eslint-disable-next-line @next/internal/typechecked-require -- Why not relative imports?\n  require('next/dist/client/components/layout-router')\n  hydrate(instrumentationHooks)\n})\n"], "names": ["appBootstrap", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate"], "mappings": "AAAA,4EAA4E;AAC5E,sCAAsC;AACtC,OAAO,gBAAe;AACtB,SAASA,YAAY,QAAQ,kBAAiB;AAE9C,MAAMC,uBACJ,8EAA8E;AAC9EC,QAAQ;AAEVF,aAAa;IACX,MAAM,EAAEG,OAAO,EAAE,GAAGD,QAAQ;IAC5B,yDAAyD;IACzD,2FAA2F;IAC3FA,QAAQ;IACR,2FAA2F;IAC3FA,QAAQ;IACRC,QAAQF;AACV", "ignoreList": [0]}