{"version": 3, "sources": ["../../../src/server/route-matchers/app-page-route-matcher.ts"], "sourcesContent": ["import { RouteMatcher } from './route-matcher'\nimport type { AppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\n\nexport class AppPageRouteMatcher extends RouteMatcher<AppPageRouteDefinition> {\n  public get identity(): string {\n    return `${this.definition.pathname}?__nextPage=${this.definition.page}`\n  }\n}\n"], "names": ["RouteMatcher", "AppPageRouteMatcher", "identity", "definition", "pathname", "page"], "mappings": "AAAA,SAASA,YAAY,QAAQ,kBAAiB;AAG9C,OAAO,MAAMC,4BAA4BD;IACvC,IAAWE,WAAmB;QAC5B,OAAO,GAAG,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAACD,UAAU,CAACE,IAAI,EAAE;IACzE;AACF", "ignoreList": [0]}