{"version": 3, "sources": ["../../../src/server/async-storage/draft-mode-provider.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/spec-extension/request'\n\nimport {\n  COOKIE_NAME_PRERENDER_BYPASS,\n  checkIsOnDemandRevalidate,\n} from '../api-utils'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nexport class DraftModeProvider {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private _isEnabled: boolean\n\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _previewModeId: string | undefined\n\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _mutableCookies: ResponseCookies\n\n  constructor(\n    previewProps: __ApiPreviewProps | undefined,\n    req: IncomingMessage | BaseNextRequest<unknown> | NextRequest,\n    cookies: ReadonlyRequestCookies,\n    mutableCookies: ResponseCookies\n  ) {\n    // The logic for draftMode() is very similar to tryGetPreviewData()\n    // but Draft Mode does not have any data associated with it.\n    const isOnDemandRevalidate =\n      previewProps &&\n      checkIsOnDemandRevalidate(req, previewProps).isOnDemandRevalidate\n\n    const cookieValue = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n\n    this._isEnabled = Boolean(\n      !isOnDemandRevalidate &&\n        cookieValue &&\n        previewProps &&\n        (cookieValue === previewProps.previewModeId ||\n          // In dev mode, the cookie can be actual hash value preview id but the preview props can still be `development-id`.\n          (process.env.NODE_ENV !== 'production' &&\n            previewProps.previewModeId === 'development-id'))\n    )\n\n    this._previewModeId = previewProps?.previewModeId\n    this._mutableCookies = mutableCookies\n  }\n\n  get isEnabled() {\n    return this._isEnabled\n  }\n\n  enable() {\n    if (!this._previewModeId) {\n      throw new Error(\n        'Invariant: previewProps missing previewModeId this should never happen'\n      )\n    }\n\n    this._mutableCookies.set({\n      name: COOKIE_NAME_PRERENDER_BYPASS,\n      value: this._previewModeId,\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n    })\n\n    this._isEnabled = true\n  }\n\n  disable() {\n    // To delete a cookie, set `expires` to a date in the past:\n    // https://tools.ietf.org/html/rfc6265#section-4.1.1\n    // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n    this._mutableCookies.set({\n      name: COOKIE_NAME_PRERENDER_BYPASS,\n      value: '',\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      expires: new Date(0),\n    })\n\n    this._isEnabled = false\n  }\n}\n"], "names": ["COOKIE_NAME_PRERENDER_BYPASS", "checkIsOnDemandRevalidate", "DraftModeProvider", "constructor", "previewProps", "req", "cookies", "mutableCookies", "isOnDemandRevalidate", "cookieValue", "get", "value", "_isEnabled", "Boolean", "previewModeId", "process", "env", "NODE_ENV", "_previewModeId", "_mutableCookies", "isEnabled", "enable", "Error", "set", "name", "httpOnly", "sameSite", "secure", "path", "disable", "expires", "Date"], "mappings": "AAMA,SACEA,4BAA4B,EAC5BC,yBAAyB,QACpB,eAAc;AAGrB,OAAO,MAAMC;IAgBXC,YACEC,YAA2C,EAC3CC,GAA6D,EAC7DC,OAA+B,EAC/BC,cAA+B,CAC/B;YAOoBD;QANpB,mEAAmE;QACnE,4DAA4D;QAC5D,MAAME,uBACJJ,gBACAH,0BAA0BI,KAAKD,cAAcI,oBAAoB;QAEnE,MAAMC,eAAcH,eAAAA,QAAQI,GAAG,CAACV,kDAAZM,aAA2CK,KAAK;QAEpE,IAAI,CAACC,UAAU,GAAGC,QAChB,CAACL,wBACCC,eACAL,gBACCK,CAAAA,gBAAgBL,aAAaU,aAAa,IACzC,mHAAmH;QAClHC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACxBb,aAAaU,aAAa,KAAK,gBAAgB;QAGvD,IAAI,CAACI,cAAc,GAAGd,gCAAAA,aAAcU,aAAa;QACjD,IAAI,CAACK,eAAe,GAAGZ;IACzB;IAEA,IAAIa,YAAY;QACd,OAAO,IAAI,CAACR,UAAU;IACxB;IAEAS,SAAS;QACP,IAAI,CAAC,IAAI,CAACH,cAAc,EAAE;YACxB,MAAM,qBAEL,CAFK,IAAII,MACR,2EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACH,eAAe,CAACI,GAAG,CAAC;YACvBC,MAAMxB;YACNW,OAAO,IAAI,CAACO,cAAc;YAC1BO,UAAU;YACVC,UAAUX,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DU,QAAQZ,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCW,MAAM;QACR;QAEA,IAAI,CAAChB,UAAU,GAAG;IACpB;IAEAiB,UAAU;QACR,2DAA2D;QAC3D,oDAAoD;QACpD,wEAAwE;QACxE,IAAI,CAACV,eAAe,CAACI,GAAG,CAAC;YACvBC,MAAMxB;YACNW,OAAO;YACPc,UAAU;YACVC,UAAUX,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DU,QAAQZ,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCW,MAAM;YACNE,SAAS,IAAIC,KAAK;QACpB;QAEA,IAAI,CAACnB,UAAU,GAAG;IACpB;AACF", "ignoreList": [0]}