{"version": 3, "sources": ["../../../src/server/app-render/cache-signal.ts"], "sourcesContent": ["/**\n * This class is used to detect when all cache reads for a given render are settled.\n * We do this to allow for cache warming the prerender without having to continue rendering\n * the remainder of the page. This feature is really only useful when the dynamicIO flag is on\n * and should only be used in codepaths gated with this feature.\n */\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport class CacheSignal {\n  private count = 0\n  private earlyListeners: Array<() => void> = []\n  private listeners: Array<() => void> = []\n  private tickPending = false\n  private taskPending = false\n\n  private subscribedSignals: Set<CacheSignal> | null = null\n\n  constructor() {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      // we rely on `process.nextTick`, which is not supported in edge\n      throw new InvariantError(\n        'CacheSignal cannot be used in the edge runtime, because `dynamicIO` does not support it.'\n      )\n    }\n  }\n\n  private noMorePendingCaches() {\n    if (!this.tickPending) {\n      this.tickPending = true\n      process.nextTick(() => {\n        this.tickPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.earlyListeners.length; i++) {\n            this.earlyListeners[i]()\n          }\n          this.earlyListeners.length = 0\n        }\n      })\n    }\n    if (!this.taskPending) {\n      this.taskPending = true\n      setTimeout(() => {\n        this.taskPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.listeners.length; i++) {\n            this.listeners[i]()\n          }\n          this.listeners.length = 0\n        }\n      }, 0)\n    }\n  }\n\n  /**\n   * This promise waits until there are no more in progress cache reads but no later.\n   * This allows for adding more cache reads after to delay cacheReady.\n   */\n  inputReady() {\n    return new Promise<void>((resolve) => {\n      this.earlyListeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  /**\n   * If there are inflight cache reads this Promise can resolve in a microtask however\n   * if there are no inflight cache reads then we wait at least one task to allow initial\n   * cache reads to be initiated.\n   */\n  cacheReady() {\n    return new Promise<void>((resolve) => {\n      this.listeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  beginRead() {\n    this.count++\n\n    if (this.subscribedSignals !== null) {\n      for (const subscriber of this.subscribedSignals) {\n        subscriber.beginRead()\n      }\n    }\n  }\n\n  endRead() {\n    if (this.count === 0) {\n      throw new InvariantError(\n        'CacheSignal got more endRead() calls than beginRead() calls'\n      )\n    }\n\n    // If this is the last read we need to wait a task before we can claim the cache is settled.\n    // The cache read will likely ping a Server Component which can read from the cache again and this\n    // will play out in a microtask so we need to only resolve pending listeners if we're still at 0\n    // after at least one task.\n    // We only want one task scheduled at a time so when we hit count 1 we don't decrement the counter immediately.\n    // If intervening reads happen before the scheduled task runs they will never observe count 1 preventing reentrency\n    this.count--\n    if (this.count === 0) {\n      this.noMorePendingCaches()\n    }\n\n    if (this.subscribedSignals !== null) {\n      for (const subscriber of this.subscribedSignals) {\n        subscriber.endRead()\n      }\n    }\n  }\n\n  trackRead<T>(promise: Promise<T>) {\n    this.beginRead()\n    // `promise.finally()` still rejects, so don't use it here to avoid unhandled rejections\n    const onFinally = this.endRead.bind(this)\n    promise.then(onFinally, onFinally)\n    return promise\n  }\n\n  subscribeToReads(subscriber: CacheSignal): () => void {\n    if (subscriber === this) {\n      throw new InvariantError('A CacheSignal cannot subscribe to itself')\n    }\n    if (this.subscribedSignals === null) {\n      this.subscribedSignals = new Set()\n    }\n    this.subscribedSignals.add(subscriber)\n\n    // we'll notify the subscriber of each endRead() on this signal,\n    // so we need to give it a corresponding beginRead() for each read we have in flight now.\n    for (let i = 0; i < this.count; i++) {\n      subscriber.beginRead()\n    }\n\n    return this.unsubscribeFromReads.bind(this, subscriber)\n  }\n\n  unsubscribeFromReads(subscriber: CacheSignal) {\n    if (!this.subscribedSignals) {\n      return\n    }\n    this.subscribedSignals.delete(subscriber)\n\n    // we don't need to set the set back to `null` if it's empty --\n    // if other signals are subscribing to this one, it'll likely get more subscriptions later,\n    // so we'd have to allocate a fresh set again when that happens.\n  }\n}\n"], "names": ["CacheSignal", "constructor", "count", "earlyListeners", "listeners", "tickPending", "taskPending", "subscribedSignals", "process", "env", "NEXT_RUNTIME", "InvariantError", "noMorePendingCaches", "nextTick", "i", "length", "setTimeout", "inputReady", "Promise", "resolve", "push", "cacheReady", "beginRead", "subscriber", "endRead", "trackRead", "promise", "onFinally", "bind", "then", "subscribeToReads", "Set", "add", "unsubscribeFromReads", "delete"], "mappings": "AAAA;;;;;CAKC;;;;+BAIYA;;;eAAAA;;;gCAFkB;AAExB,MAAMA;IASXC,aAAc;aARNC,QAAQ;aACRC,iBAAoC,EAAE;aACtCC,YAA+B,EAAE;aACjCC,cAAc;aACdC,cAAc;aAEdC,oBAA6C;QAGnD,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvC,gEAAgE;YAChE,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,6FADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEQC,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;YACrB,IAAI,CAACA,WAAW,GAAG;YACnBG,QAAQK,QAAQ,CAAC;gBACf,IAAI,CAACR,WAAW,GAAG;gBACnB,IAAI,IAAI,CAACH,KAAK,KAAK,GAAG;oBACpB,IAAK,IAAIY,IAAI,GAAGA,IAAI,IAAI,CAACX,cAAc,CAACY,MAAM,EAAED,IAAK;wBACnD,IAAI,CAACX,cAAc,CAACW,EAAE;oBACxB;oBACA,IAAI,CAACX,cAAc,CAACY,MAAM,GAAG;gBAC/B;YACF;QACF;QACA,IAAI,CAAC,IAAI,CAACT,WAAW,EAAE;YACrB,IAAI,CAACA,WAAW,GAAG;YACnBU,WAAW;gBACT,IAAI,CAACV,WAAW,GAAG;gBACnB,IAAI,IAAI,CAACJ,KAAK,KAAK,GAAG;oBACpB,IAAK,IAAIY,IAAI,GAAGA,IAAI,IAAI,CAACV,SAAS,CAACW,MAAM,EAAED,IAAK;wBAC9C,IAAI,CAACV,SAAS,CAACU,EAAE;oBACnB;oBACA,IAAI,CAACV,SAAS,CAACW,MAAM,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA;;;GAGC,GACDE,aAAa;QACX,OAAO,IAAIC,QAAc,CAACC;YACxB,IAAI,CAAChB,cAAc,CAACiB,IAAI,CAACD;YACzB,IAAI,IAAI,CAACjB,KAAK,KAAK,GAAG;gBACpB,IAAI,CAACU,mBAAmB;YAC1B;QACF;IACF;IAEA;;;;GAIC,GACDS,aAAa;QACX,OAAO,IAAIH,QAAc,CAACC;YACxB,IAAI,CAACf,SAAS,CAACgB,IAAI,CAACD;YACpB,IAAI,IAAI,CAACjB,KAAK,KAAK,GAAG;gBACpB,IAAI,CAACU,mBAAmB;YAC1B;QACF;IACF;IAEAU,YAAY;QACV,IAAI,CAACpB,KAAK;QAEV,IAAI,IAAI,CAACK,iBAAiB,KAAK,MAAM;YACnC,KAAK,MAAMgB,cAAc,IAAI,CAAChB,iBAAiB,CAAE;gBAC/CgB,WAAWD,SAAS;YACtB;QACF;IACF;IAEAE,UAAU;QACR,IAAI,IAAI,CAACtB,KAAK,KAAK,GAAG;YACpB,MAAM,qBAEL,CAFK,IAAIS,8BAAc,CACtB,gEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,4FAA4F;QAC5F,kGAAkG;QAClG,gGAAgG;QAChG,2BAA2B;QAC3B,+GAA+G;QAC/G,mHAAmH;QACnH,IAAI,CAACT,KAAK;QACV,IAAI,IAAI,CAACA,KAAK,KAAK,GAAG;YACpB,IAAI,CAACU,mBAAmB;QAC1B;QAEA,IAAI,IAAI,CAACL,iBAAiB,KAAK,MAAM;YACnC,KAAK,MAAMgB,cAAc,IAAI,CAAChB,iBAAiB,CAAE;gBAC/CgB,WAAWC,OAAO;YACpB;QACF;IACF;IAEAC,UAAaC,OAAmB,EAAE;QAChC,IAAI,CAACJ,SAAS;QACd,wFAAwF;QACxF,MAAMK,YAAY,IAAI,CAACH,OAAO,CAACI,IAAI,CAAC,IAAI;QACxCF,QAAQG,IAAI,CAACF,WAAWA;QACxB,OAAOD;IACT;IAEAI,iBAAiBP,UAAuB,EAAc;QACpD,IAAIA,eAAe,IAAI,EAAE;YACvB,MAAM,qBAA8D,CAA9D,IAAIZ,8BAAc,CAAC,6CAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAA6D;QACrE;QACA,IAAI,IAAI,CAACJ,iBAAiB,KAAK,MAAM;YACnC,IAAI,CAACA,iBAAiB,GAAG,IAAIwB;QAC/B;QACA,IAAI,CAACxB,iBAAiB,CAACyB,GAAG,CAACT;QAE3B,gEAAgE;QAChE,yFAAyF;QACzF,IAAK,IAAIT,IAAI,GAAGA,IAAI,IAAI,CAACZ,KAAK,EAAEY,IAAK;YACnCS,WAAWD,SAAS;QACtB;QAEA,OAAO,IAAI,CAACW,oBAAoB,CAACL,IAAI,CAAC,IAAI,EAAEL;IAC9C;IAEAU,qBAAqBV,UAAuB,EAAE;QAC5C,IAAI,CAAC,IAAI,CAAChB,iBAAiB,EAAE;YAC3B;QACF;QACA,IAAI,CAACA,iBAAiB,CAAC2B,MAAM,CAACX;IAE9B,+DAA+D;IAC/D,2FAA2F;IAC3F,gEAAgE;IAClE;AACF", "ignoreList": [0]}