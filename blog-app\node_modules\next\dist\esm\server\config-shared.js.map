{"version": 3, "sources": ["../../src/server/config-shared.ts"], "sourcesContent": ["import os from 'os'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { Header, Redirect, Rewrite } from '../lib/load-custom-routes'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type {\n  ImageConfig,\n  ImageConfigComplete,\n} from '../shared/lib/image-config'\nimport type { SubresourceIntegrityAlgorithm } from '../build/webpack/plugins/subresource-integrity-plugin'\nimport type { WEB_VITALS } from '../shared/lib/utils'\nimport type { NextParsedUrlQuery } from './request-meta'\nimport type { SizeLimit } from '../types'\nimport type { SupportedTestRunners } from '../cli/next-test'\nimport type { ExperimentalPPRConfig } from './lib/experimental/ppr'\nimport { INFINITE_CACHE } from '../lib/constants'\nimport type {\n  ManifestRewriteRoute,\n  ManifestHeaderRoute,\n  ManifestRedirectRoute,\n  RouteType,\n} from '../build'\nimport { isStableBuild } from '../shared/lib/canary-only'\n\nexport type NextConfigComplete = Required<NextConfig> & {\n  images: Required<ImageConfigComplete>\n  typescript: Required<TypeScriptConfig>\n  configOrigin?: string\n  configFile?: string\n  configFileName: string\n  // override NextConfigComplete.experimental.htmlLimitedBots to string\n  // because it's not defined in NextConfigComplete.experimental\n  htmlLimitedBots: string | undefined\n  experimental: Omit<ExperimentalConfig, 'turbo'>\n}\n\nexport type AdapterOutputs = Array<{\n  id: string\n  fallbackID?: string\n  runtime?: 'nodejs' | 'edge'\n  pathname: string\n  allowQuery?: string[]\n  config?: {\n    maxDuration?: number\n    expiration?: number\n    revalidate?: number\n  }\n  assets?: Record<string, string>\n  filePath: string\n  type: RouteType\n}>\n\nexport interface NextAdapter {\n  name: string\n  modifyConfig(\n    config: NextConfigComplete\n  ): Promise<NextConfigComplete> | NextConfigComplete\n  onBuildComplete(ctx: {\n    routes: {\n      headers: Array<ManifestHeaderRoute>\n      redirects: Array<ManifestRedirectRoute>\n      rewrites: {\n        beforeFiles: Array<ManifestRewriteRoute>\n        afterFiles: Array<ManifestRewriteRoute>\n        fallback: Array<ManifestRewriteRoute>\n      }\n      dynamicRoutes: Array<{}>\n    }\n    outputs: AdapterOutputs\n  }): Promise<void> | void\n}\n\nexport type I18NDomains = readonly DomainLocale[]\n\nexport interface I18NConfig {\n  defaultLocale: string\n  domains?: I18NDomains\n  localeDetection?: false\n  locales: readonly string[]\n}\n\nexport interface DomainLocale {\n  defaultLocale: string\n  domain: string\n  http?: true\n  locales?: readonly string[]\n}\n\nexport interface ESLintConfig {\n  /** Only run ESLint on these directories with `next lint` and `next build`. */\n  dirs?: string[]\n  /** Do not run ESLint during production builds (`next build`). */\n  ignoreDuringBuilds?: boolean\n}\n\nexport interface TypeScriptConfig {\n  /** Do not run TypeScript during production builds (`next build`). */\n  ignoreBuildErrors?: boolean\n  /** Relative path to a custom tsconfig file */\n  tsconfigPath?: string\n}\n\nexport interface EmotionConfig {\n  sourceMap?: boolean\n  autoLabel?: 'dev-only' | 'always' | 'never'\n  labelFormat?: string\n  importMap?: {\n    [importName: string]: {\n      [exportName: string]: {\n        canonicalImport?: [string, string]\n        styledBaseImport?: [string, string]\n      }\n    }\n  }\n}\n\nexport interface StyledComponentsConfig {\n  /**\n   * Enabled by default in development, disabled in production to reduce file size,\n   * setting this will override the default for all environments.\n   */\n  displayName?: boolean\n  topLevelImportPaths?: string[]\n  ssr?: boolean\n  fileName?: boolean\n  meaninglessFileNames?: string[]\n  minify?: boolean\n  transpileTemplateLiterals?: boolean\n  namespace?: string\n  pure?: boolean\n  cssProp?: boolean\n}\n\ntype JSONValue =\n  | string\n  | number\n  | boolean\n  | JSONValue[]\n  | { [k: string]: JSONValue }\n\n/**\n * @deprecated Use `TurbopackRuleConfigItem` instead.\n */\nexport type TurbopackLoaderItem =\n  | string\n  | {\n      loader: string\n      // At the moment, Turbopack options must be JSON-serializable, so restrict values.\n      options: Record<string, JSONValue>\n    }\n\nexport type TurbopackRuleCondition = {\n  path: string | RegExp\n}\n\nexport type TurbopackRuleConfigItemOrShortcut =\n  | TurbopackLoaderItem[]\n  | TurbopackRuleConfigItem\n\nexport type TurbopackRuleConfigItemOptions = {\n  loaders: TurbopackLoaderItem[]\n  as?: string\n}\n\nexport type TurbopackRuleConfigItem =\n  | TurbopackRuleConfigItemOptions\n  | { [condition: string]: TurbopackRuleConfigItem }\n  | false\n\nexport interface TurbopackOptions {\n  /**\n   * (`next --turbopack` only) A mapping of aliased imports to modules to load in their place.\n   *\n   * @see [Resolve Alias](https://nextjs.org/docs/app/api-reference/next-config-js/turbo#resolve-alias)\n   */\n  resolveAlias?: Record<\n    string,\n    string | string[] | Record<string, string | string[]>\n  >\n\n  /**\n   * (`next --turbopack` only) A list of extensions to resolve when importing files.\n   *\n   * @see [Resolve Extensions](https://nextjs.org/docs/app/api-reference/next-config-js/turbo#resolve-extensions)\n   */\n  resolveExtensions?: string[]\n\n  /**\n   * (`next --turbopack` only) A list of webpack loaders to apply when running with Turbopack.\n   *\n   * @see [Turbopack Loaders](https://nextjs.org/docs/app/api-reference/next-config-js/turbo#webpack-loaders)\n   */\n  rules?: Record<string, TurbopackRuleConfigItemOrShortcut>\n\n  /**\n   * (`next --turbopack` only) A list of conditions to apply when running webpack loaders with Turbopack.\n   *\n   * @see [Turbopack Loaders](https://nextjs.org/docs/app/api-reference/next-config-js/turbo#webpack-loaders)\n   */\n  conditions?: Record<string, TurbopackRuleCondition>\n\n  /**\n   * The module ID strategy to use for Turbopack.\n   * If not set, the default is `'named'` for development and `'deterministic'`\n   * for production.\n   */\n  moduleIds?: 'named' | 'deterministic'\n\n  /**\n   * This is the repo root usually and only files above this\n   * directory can be resolved by turbopack.\n   */\n  root?: string\n}\n\nexport interface DeprecatedExperimentalTurboOptions extends TurbopackOptions {\n  /**\n   * (`next --turbopack` only) A list of webpack loaders to apply when running with Turbopack.\n   *\n   * @deprecated Use `rules` instead.\n   * @see [Turbopack Loaders](https://nextjs.org/docs/app/api-reference/next-config-js/turbo#webpack-loaders)\n   */\n  loaders?: Record<string, TurbopackLoaderItem[]>\n\n  /**\n   * A target memory limit for turbo, in bytes.\n   * @deprecated Use `experimental.turbopackMemoryLimit` instead.\n   */\n  memoryLimit?: number\n\n  /**\n   * Enable minification. Defaults to true in build mode and false in dev mode.\n   * @deprecated Use `experimental.turbopackMinify` instead.\n   */\n  minify?: boolean\n\n  /**\n   * Enable tree shaking for the turbopack dev server and build.\n   * @deprecated Use `experimental.turbopackTreeShaking` instead.\n   */\n  treeShaking?: boolean\n\n  /**\n   * Enable source maps. Defaults to true.\n   * @deprecated Use `experimental.turbopackSourceMaps` instead.\n   */\n  sourceMaps?: boolean\n}\n\nexport interface WebpackConfigContext {\n  /** Next.js root directory */\n  dir: string\n  /** Indicates if the compilation will be done in development */\n  dev: boolean\n  /** It's `true` for server-side compilation, and `false` for client-side compilation */\n  isServer: boolean\n  /**  The build id, used as a unique identifier between builds */\n  buildId: string\n  /** The next.config.js merged with default values */\n  config: NextConfigComplete\n  /** Default loaders used internally by Next.js */\n  defaultLoaders: {\n    /** Default babel-loader configuration */\n    babel: any\n  }\n  /** Number of total Next.js pages */\n  totalPages: number\n  /** The webpack configuration */\n  webpack: any\n  /** The current server runtime */\n  nextRuntime?: 'nodejs' | 'edge'\n}\n\nexport interface NextJsWebpackConfig {\n  (\n    /** Existing Webpack config */\n    config: any,\n    context: WebpackConfigContext\n  ): any\n}\n\n/**\n * Set of options for the react compiler next.js\n * currently supports.\n *\n * This can be changed without breaking changes while supporting\n * react compiler in the experimental phase.\n */\nexport interface ReactCompilerOptions {\n  compilationMode?: 'infer' | 'annotation' | 'all'\n  panicThreshold?: 'ALL_ERRORS' | 'CRITICAL_ERRORS' | 'NONE'\n}\n\nexport interface IncomingRequestLoggingConfig {\n  /**\n   * A regular expression array to match incoming requests that should not be logged.\n   * You can specify multiple patterns to match incoming requests that should not be logged.\n   */\n  ignore?: RegExp[]\n}\n\nexport interface LoggingConfig {\n  fetches?: {\n    fullUrl?: boolean\n    /**\n     * If true, fetch requests that are restored from the HMR cache are logged\n     * during an HMR refresh request, i.e. when editing a server component.\n     */\n    hmrRefreshes?: boolean\n  }\n\n  /**\n   * If set to false, incoming request logging is disabled.\n   * You can specify a pattern to match incoming requests that should not be logged.\n   */\n  incomingRequests?: boolean | IncomingRequestLoggingConfig\n}\n\nexport interface ExperimentalConfig {\n  adapterPath?: string\n  useSkewCookie?: boolean\n  nodeMiddleware?: boolean\n  cacheHandlers?: {\n    default?: string\n    remote?: string\n    static?: string\n    [handlerName: string]: string | undefined\n  }\n  multiZoneDraftMode?: boolean\n  appNavFailHandling?: boolean\n  prerenderEarlyExit?: boolean\n  linkNoTouchStart?: boolean\n  caseSensitiveRoutes?: boolean\n  clientSegmentCache?: boolean | 'client-only'\n  dynamicOnHover?: boolean\n  appDocumentPreloading?: boolean\n  preloadEntriesOnStart?: boolean\n  /** @default true */\n  strictNextHead?: boolean\n  clientRouterFilter?: boolean\n  clientRouterFilterRedirects?: boolean\n  /**\n   * This config can be used to override the cache behavior for the client router.\n   * These values indicate the time, in seconds, that the cache should be considered\n   * reusable. When the `prefetch` Link prop is left unspecified, this will use the `dynamic` value.\n   * When the `prefetch` Link prop is set to `true`, this will use the `static` value.\n   */\n  staleTimes?: {\n    dynamic?: number\n    static?: number\n  }\n  cacheLife?: {\n    [profile: string]: {\n      // How long the client can cache a value without checking with the server.\n      stale?: number\n      // How frequently you want the cache to refresh on the server.\n      // Stale values may be served while revalidating.\n      revalidate?: number\n      // In the worst case scenario, where you haven't had traffic in a while,\n      // how stale can a value be until you prefer deopting to dynamic.\n      // Must be longer than revalidate.\n      expire?: number\n    }\n  }\n  // decimal for percent for possible false positives\n  // e.g. 0.01 for 10% potential false matches lower\n  // percent increases size of the filter\n  clientRouterFilterAllowedRate?: number\n  externalMiddlewareRewritesResolve?: boolean\n  extensionAlias?: Record<string, any>\n  allowedRevalidateHeaderKeys?: string[]\n  fetchCacheKeyPrefix?: string\n  imgOptConcurrency?: number | null\n  imgOptTimeoutInSeconds?: number\n  imgOptMaxInputPixels?: number\n  imgOptSequentialRead?: boolean | null\n  optimisticClientCache?: boolean\n  /**\n   * @deprecated use config.expireTime instead\n   */\n  expireTime?: number\n  middlewarePrefetch?: 'strict' | 'flexible'\n  manualClientBasePath?: boolean\n  /**\n   * CSS Chunking strategy. Defaults to `true` (\"loose\" mode), which guesses dependencies\n   * between CSS files to keep ordering of them.\n   * An alternative is 'strict', which will try to keep correct ordering as\n   * much as possible, even when this leads to many requests.\n   */\n  cssChunking?: boolean | 'strict'\n  disablePostcssPresetEnv?: boolean\n  cpus?: number\n  memoryBasedWorkersCount?: boolean\n  proxyTimeout?: number\n  isrFlushToDisk?: boolean\n  workerThreads?: boolean\n  // optimizeCss can be boolean or critters' option object\n  // Use Record<string, unknown> as critters doesn't export its Option type\n  // https://github.com/GoogleChromeLabs/critters/blob/a590c05f9197b656d2aeaae9369df2483c26b072/packages/critters/src/index.d.ts\n  optimizeCss?: boolean | Record<string, unknown>\n  nextScriptWorkers?: boolean\n  scrollRestoration?: boolean\n  externalDir?: boolean\n  amp?: {\n    optimizer?: any\n    validator?: string\n    skipValidation?: boolean\n  }\n  disableOptimizedLoading?: boolean\n  gzipSize?: boolean\n  craCompat?: boolean\n  esmExternals?: boolean | 'loose'\n  fullySpecified?: boolean\n  urlImports?: NonNullable<webpack.Configuration['experiments']>['buildHttp']\n  swcTraceProfiling?: boolean\n  forceSwcTransforms?: boolean\n\n  swcPlugins?: Array<[string, Record<string, unknown>]>\n  largePageDataBytes?: number\n  /**\n   * If set to `false`, webpack won't fall back to polyfill Node.js modules in the browser\n   * Full list of old polyfills is accessible here:\n   * [webpack/webpack#ModuleNotoundError.js#L13-L42](https://github.com/webpack/webpack/blob/2a0536cf510768111a3a6dceeb14cb79b9f59273/lib/ModuleNotFoundError.js#L13-L42)\n   */\n  fallbackNodePolyfills?: false\n  sri?: {\n    algorithm?: SubresourceIntegrityAlgorithm\n  }\n\n  webVitalsAttribution?: Array<(typeof WEB_VITALS)[number]>\n\n  /**\n   * Automatically apply the \"modularizeImports\" optimization to imports of the specified packages.\n   */\n  optimizePackageImports?: string[]\n\n  /**\n   * Optimize React APIs for server builds.\n   */\n  optimizeServerReact?: boolean\n\n  /**\n   * @deprecated Use `config.turbopack` instead.\n   */\n  turbo?: DeprecatedExperimentalTurboOptions\n\n  /**\n   * A target memory limit for turbo, in bytes.\n   */\n  turbopackMemoryLimit?: number\n\n  /**\n   * Enable minification. Defaults to true in build mode and false in dev mode.\n   */\n  turbopackMinify?: boolean\n\n  /**\n   * Enable scope hoisting. Defaults to true in build mode. Always disabled in development mode.\n   */\n  turbopackScopeHoisting?: boolean\n\n  /**\n   * Enable persistent caching for the turbopack dev server and build.\n   */\n  turbopackPersistentCaching?: boolean\n\n  /**\n   * Enable source maps. Defaults to true.\n   */\n  turbopackSourceMaps?: boolean\n\n  /**\n   * Enable tree shaking for the turbopack dev server and build.\n   */\n  turbopackTreeShaking?: boolean\n\n  /**\n   * Enable removing unused exports for turbopack dev server and build.\n   */\n  turbopackRemoveUnusedExports?: boolean\n\n  /**\n   * For use with `@next/mdx`. Compile MDX files using the new Rust compiler.\n   * @see https://nextjs.org/docs/app/api-reference/next-config-js/mdxRs\n   */\n  mdxRs?:\n    | boolean\n    | {\n        development?: boolean\n        jsx?: boolean\n        jsxRuntime?: string\n        jsxImportSource?: string\n        providerImportSource?: string\n        mdxType?: 'gfm' | 'commonmark'\n      }\n\n  /**\n   * Generate Route types and enable type checking for Link and Router.push, etc.\n   * @see https://nextjs.org/docs/app/api-reference/next-config-js/typedRoutes\n   */\n  typedRoutes?: boolean\n\n  /**\n   * Enable type-checking and autocompletion for environment variables.\n   *\n   * @default false\n   */\n  typedEnv?: boolean\n\n  /**\n   * Runs the compilations for server and edge in parallel instead of in serial.\n   * This will make builds faster if there is enough server and edge functions\n   * in the application at the cost of more memory.\n   *\n   * NOTE: This option is only valid when the build process can use workers. See\n   * the documentation for `webpackBuildWorker` for more details.\n   */\n  parallelServerCompiles?: boolean\n\n  /**\n   * Runs the logic to collect build traces for the server routes in parallel\n   * with other work during the compilation. This will increase the speed of\n   * the build at the cost of more memory. This option may incur some additional\n   * work compared to if the option was disabled since the work is started\n   * before data from the client compilation is available to potentially reduce\n   * the amount of code that needs to be traced. Despite that, this may still\n   * result in faster builds for some applications.\n   *\n   * Valid values are:\n   * - `true`: Collect the server build traces in parallel.\n   * - `false`: Do not collect the server build traces in parallel.\n   * - `undefined`: Collect server build traces in parallel only in the `experimental-compile` mode.\n   *\n   * NOTE: This option is only valid when the build process can use workers. See\n   * the documentation for `webpackBuildWorker` for more details.\n   */\n  parallelServerBuildTraces?: boolean\n\n  /**\n   * Run the Webpack build in a separate process to optimize memory usage during build.\n   * Valid values are:\n   * - `false`: Disable the Webpack build worker\n   * - `true`: Enable the Webpack build worker\n   * - `undefined`: Enable the Webpack build worker only if the webpack config is not customized\n   */\n  webpackBuildWorker?: boolean\n\n  /**\n   * Enables optimizations to reduce memory usage in Webpack. This reduces the max size of the heap\n   * but may increase compile times slightly.\n   * Valid values are:\n   * - `false`: Disable Webpack memory optimizations (default).\n   * - `true`: Enables Webpack memory optimizations.\n   */\n  webpackMemoryOptimizations?: boolean\n\n  /**\n   * The array of the meta tags to the client injected by tracing propagation data.\n   */\n  clientTraceMetadata?: string[]\n\n  /**\n   * Enables experimental Partial Prerendering feature of Next.js.\n   * Using this feature will enable the `react@experimental` for the `app` directory.\n   */\n  ppr?: ExperimentalPPRConfig\n\n  /**\n   * Enables experimental taint APIs in React.\n   * Using this feature will enable the `react@experimental` for the `app` directory.\n   */\n  taint?: boolean\n\n  /**\n   * Enables the Back/Forward Cache for the router.\n   */\n  routerBFCache?: boolean\n\n  /**\n   * Uninstalls all \"unhandledRejection\" and \"uncaughtException\" listeners from\n   * the global process so that we can override the behavior, which in some\n   * runtimes is to exit the process.\n   *\n   * This is experimental until we've considered the impact in various\n   * deployment environments.\n   */\n  removeUncaughtErrorAndRejectionListeners?: boolean\n\n  /**\n   * During an RSC request, validates that the request headers match the\n   * cache-busting search parameter sent by the client.\n   */\n  validateRSCRequestHeaders?: boolean\n\n  serverActions?: {\n    /**\n     * Allows adjusting body parser size limit for server actions.\n     */\n    bodySizeLimit?: SizeLimit\n\n    /**\n     * Allowed origins that can bypass Server Action's CSRF check. This is helpful\n     * when you have reverse proxy in front of your app.\n     * @example\n     * [\"my-app.com\", \"*.my-app.com\"]\n     */\n    allowedOrigins?: string[]\n  }\n\n  /**\n   * enables the minification of server code.\n   */\n  serverMinification?: boolean\n\n  /**\n   * Enables source maps while generating static pages.\n   * Helps with errors during the prerender phase in `next build`.\n   */\n  enablePrerenderSourceMaps?: boolean\n\n  /**\n   * Enables source maps generation for the server production bundle.\n   */\n  serverSourceMaps?: boolean\n\n  /**\n   * @internal Used by the Next.js internals only.\n   */\n  trustHostHeader?: boolean\n  /**\n   * @internal Used by the Next.js internals only.\n   */\n  isExperimentalCompile?: boolean\n\n  useWasmBinary?: boolean\n\n  /**\n   * Use lightningcss instead of postcss-loader\n   */\n  useLightningcss?: boolean\n\n  /**\n   * Enables view transitions by using the {@link https://github.com/facebook/react/pull/31975 unstable_ViewTransition} Component.\n   */\n  viewTransition?: boolean\n\n  /**\n   * Enables `fetch` requests to be proxied to the experimental test proxy server\n   */\n  testProxy?: boolean\n\n  /**\n   * Set a default test runner to be used by `next experimental-test`.\n   */\n  defaultTestRunner?: SupportedTestRunners\n  /**\n   * Allow NODE_ENV=development even for `next build`.\n   */\n  allowDevelopmentBuild?: true\n  /**\n   * @deprecated use `config.bundlePagesRouterDependencies` instead\n   *\n   */\n  bundlePagesExternals?: boolean\n  /**\n   * @deprecated use `config.serverExternalPackages` instead\n   *\n   */\n  serverComponentsExternalPackages?: string[]\n  /**\n   * Enable experimental React compiler optimization.\n   * Configuration accepts partial config object to the compiler, if provided\n   * compiler will be enabled.\n   */\n  reactCompiler?: boolean | ReactCompilerOptions\n\n  /**\n   * The number of times to retry static generation (per page) before giving up.\n   */\n  staticGenerationRetryCount?: number\n\n  /**\n   * The amount of pages to export per worker during static generation.\n   */\n  staticGenerationMaxConcurrency?: number\n\n  /**\n   * The minimum number of pages to be chunked into each export worker.\n   */\n  staticGenerationMinPagesPerWorker?: number\n\n  /**\n   * Allows previously fetched data to be re-used when editing server components.\n   */\n  serverComponentsHmrCache?: boolean\n\n  /**\n   * When enabled, will cause IO in App Router to be excluded from prerenders,\n   * unless explicitly cached. This also enables the experimental Partial\n   * Prerendering feature of Next.js, and it enables `react@experimental` being\n   * used for the `app` directory.\n   */\n  dynamicIO?: boolean\n\n  /**\n   * Render <style> tags inline in the HTML for imported CSS assets.\n   * Supports app-router in production mode only.\n   */\n  inlineCss?: boolean\n\n  // TODO: Remove this config when the API is stable.\n  /**\n   * This config allows you to enable the experimental navigation API `forbidden` and `unauthorized`.\n   */\n  authInterrupts?: boolean\n\n  /**\n   * Enables the use of the `\"use cache\"` directive.\n   */\n  useCache?: boolean\n\n  /**\n   * Enables detection and reporting of slow modules during development builds.\n   * Enabling this may impact build performance to ensure accurate measurements.\n   */\n  slowModuleDetection?: {\n    /**\n     * The time threshold in milliseconds for identifying slow modules.\n     * Modules taking longer than this build time threshold will be reported.\n     */\n    buildTimeThresholdMs: number\n  }\n\n  /**\n   * Enables using the global-not-found.js file in the app directory\n   *\n   */\n  globalNotFound?: boolean\n\n  /**\n   * Enable segment viewer for the app directory in Next.js DevTools.\n   */\n  devtoolSegmentExplorer?: boolean\n\n  /**\n   * Enable new panel UI for the Next.js DevTools.\n   */\n  devtoolNewPanelUI?: boolean\n\n  /**\n   * Enable debug information to be forwarded from browser to dev server stdout/stderr\n   */\n  browserDebugInfoInTerminal?:\n    | boolean\n    | {\n        /**\n         * Option to limit stringification at a specific nesting depth when logging circular objects.\n         * @default 5\n         */\n        depthLimit?: number\n\n        /**\n         * Maximum number of properties/elements to stringify when logging objects/arrays with circular references.\n         * @default 100\n         */\n        edgeLimit?: number\n        /**\n         * Whether to include source location information in debug output when available\n         */\n        showSourceLocation?: boolean\n      }\n\n  /**\n   * When enabled, will only opt-in to special smooth scroll handling when\n   * data-scroll-behavior=\"smooth\" is present on the <html> element.\n   * This will be the default, non-configurable behavior in the next major version.\n   *\n   * @default false\n   */\n  optimizeRouterScrolling?: boolean\n}\n\nexport type ExportPathMap = {\n  [path: string]: {\n    page: string\n    query?: NextParsedUrlQuery\n\n    /**\n     * When true, this indicates that this is a pages router page that should\n     * be rendered as a fallback.\n     *\n     * @internal\n     */\n    _pagesFallback?: boolean\n\n    /**\n     * The locale that this page should be rendered in.\n     *\n     * @internal\n     */\n    _locale?: string\n\n    /**\n     * The path that was used to generate the page.\n     *\n     * @internal\n     */\n    _ssgPath?: string\n\n    /**\n     * The parameters that are currently unknown.\n     *\n     * @internal\n     */\n    _fallbackRouteParams?: readonly string[]\n\n    /**\n     * @internal\n     */\n    _isAppDir?: boolean\n\n    /**\n     * @internal\n     */\n    _isDynamicError?: boolean\n\n    /**\n     * @internal\n     */\n    _isRoutePPREnabled?: boolean\n\n    /**\n     * When true, the page is prerendered as a fallback shell, while allowing\n     * any dynamic accesses to result in an empty shell. This is the case when\n     * the app has `experimental.ppr` and `experimental.dynamicIO` enabled, and\n     * there are also routes prerendered with a more complete set of params.\n     * Prerendering those routes would catch any invalid dynamic accesses.\n     *\n     * @internal\n     */\n    _allowEmptyStaticShell?: boolean\n  }\n}\n\n/**\n * Next.js can be configured through a `next.config.js` file in the root of your project directory.\n *\n * This can change the behavior, enable experimental features, and configure other advanced options.\n *\n * Read more: [Next.js Docs: `next.config.js`](https://nextjs.org/docs/app/api-reference/config/next-config-js)\n */\nexport interface NextConfig extends Record<string, any> {\n  allowedDevOrigins?: string[]\n\n  exportPathMap?: (\n    defaultMap: ExportPathMap,\n    ctx: {\n      dev: boolean\n      dir: string\n      outDir: string | null\n      distDir: string\n      buildId: string\n    }\n  ) => Promise<ExportPathMap> | ExportPathMap\n\n  /**\n   * Internationalization configuration\n   *\n   * @see [Internationalization docs](https://nextjs.org/docs/advanced-features/i18n-routing)\n   */\n  i18n?: I18NConfig | null\n\n  /**\n   * @since version 11\n   * @see [ESLint configuration](https://nextjs.org/docs/app/api-reference/config/eslint)\n   */\n  eslint?: ESLintConfig\n\n  /**\n   * @see [Next.js TypeScript documentation](https://nextjs.org/docs/app/api-reference/config/typescript)\n   */\n  typescript?: TypeScriptConfig\n\n  /**\n   * Headers allow you to set custom HTTP headers for an incoming request path.\n   *\n   * @see [Headers configuration documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/headers)\n   */\n  headers?: () => Promise<Header[]>\n\n  /**\n   * Rewrites allow you to map an incoming request path to a different destination path.\n   *\n   * @see [Rewrites configuration documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/rewrites)\n   */\n  rewrites?: () => Promise<\n    | Rewrite[]\n    | {\n        beforeFiles?: Rewrite[]\n        afterFiles?: Rewrite[]\n        fallback?: Rewrite[]\n      }\n  >\n\n  /**\n   * Redirects allow you to redirect an incoming request path to a different destination path.\n   *\n   * @see [Redirects configuration documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/redirects)\n   */\n  redirects?: () => Promise<Redirect[]>\n\n  /**\n   * @see [Moment.js locales excluded by default](https://nextjs.org/docs/upgrading#momentjs-locales-excluded-by-default)\n   */\n  excludeDefaultMomentLocales?: boolean\n\n  /**\n   * Before continuing to add custom webpack configuration to your application make sure Next.js doesn't already support your use-case\n   *\n   * @see [Custom Webpack Config documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/webpack)\n   */\n  webpack?: NextJsWebpackConfig | null\n\n  /**\n   * By default Next.js will redirect urls with trailing slashes to their counterpart without a trailing slash.\n   *\n   * @default false\n   * @see [Trailing Slash Configuration](https://nextjs.org/docs/app/api-reference/config/next-config-js/trailingSlash)\n   */\n  trailingSlash?: boolean\n\n  /**\n   * Next.js comes with built-in support for environment variables\n   *\n   * @see [Environment Variables documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/env)\n   */\n  env?: Record<string, string | undefined>\n\n  /**\n   * Destination directory (defaults to `.next`)\n   */\n  distDir?: string\n\n  /**\n   * The build output directory (defaults to `.next`) is now cleared by default except for the Next.js caches.\n   */\n  cleanDistDir?: boolean\n\n  /**\n   * To set up a CDN, you can set up an asset prefix and configure your CDN's origin to resolve to the domain that Next.js is hosted on.\n   *\n   * @see [CDN Support with Asset Prefix](https://nextjs.org/docs/app/api-reference/config/next-config-js/assetPrefix)\n   */\n  assetPrefix?: string\n\n  /**\n   * The default cache handler for the Pages and App Router uses the filesystem cache. This requires no configuration, however, you can customize the cache handler if you prefer.\n   *\n   * @see [Configuring Caching](https://nextjs.org/docs/app/building-your-application/deploying#configuring-caching) and the [API Reference](https://nextjs.org/docs/app/api-reference/next-config-js/incrementalCacheHandlerPath).\n   */\n  cacheHandler?: string | undefined\n\n  /**\n   * Configure the in-memory cache size in bytes. Defaults to 50 MB.\n   * If `cacheMaxMemorySize: 0`, this disables in-memory caching entirely.\n   *\n   * @see [Configuring Caching](https://nextjs.org/docs/app/building-your-application/deploying#configuring-caching).\n   */\n  cacheMaxMemorySize?: number\n\n  /**\n   * By default, `Next` will serve each file in the `pages` folder under a pathname matching the filename.\n   * To disable this behavior and prevent routing based set this to `true`.\n   *\n   * @default true\n   * @see [Disabling file-system routing](https://nextjs.org/docs/advanced-features/custom-server#disabling-file-system-routing)\n   */\n  useFileSystemPublicRoutes?: boolean\n\n  /**\n   * @see [Configuring the build ID](https://nextjs.org/docs/app/api-reference/config/next-config-js/generateBuildId)\n   */\n  generateBuildId?: () => string | null | Promise<string | null>\n\n  /** @see [Disabling ETag Configuration](https://nextjs.org/docs/app/api-reference/config/next-config-js/generateEtags) */\n  generateEtags?: boolean\n\n  /** @see [Including non-page files in the pages directory](https://nextjs.org/docs/app/api-reference/config/next-config-js/pageExtensions) */\n  pageExtensions?: string[]\n\n  /** @see [Compression documentation](https://nextjs.org/docs/app/api-reference/config/next-config-js/compress) */\n  compress?: boolean\n\n  /** @see [Disabling x-powered-by](https://nextjs.org/docs/app/api-reference/config/next-config-js/poweredByHeader) */\n  poweredByHeader?: boolean\n\n  /** @see [Using the Image Component](https://nextjs.org/docs/app/api-reference/next-config-js/images) */\n  images?: ImageConfig\n\n  /** Configure indicators in development environment */\n  devIndicators?:\n    | false\n    | {\n        /**\n         * @deprecated The dev tools indicator has it enabled by default. To disable, set `devIndicators` to `false`.\n         * */\n        appIsrStatus?: boolean\n\n        /**\n         * Show \"building...\" indicator in development\n         * @deprecated The dev tools indicator has it enabled by default. To disable, set `devIndicators` to `false`.\n         */\n        buildActivity?: boolean\n\n        /**\n         * Position of \"building...\" indicator in browser\n         * @default \"bottom-right\"\n         * @deprecated Renamed as `position`.\n         */\n        buildActivityPosition?:\n          | 'top-left'\n          | 'top-right'\n          | 'bottom-left'\n          | 'bottom-right'\n\n        /**\n         * Position of the development tools indicator in the browser window.\n         * @default \"bottom-left\"\n         * */\n        position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n      }\n\n  /**\n   * Next.js exposes some options that give you some control over how the server will dispose or keep in memory built pages in development.\n   *\n   * @see [Configuring `onDemandEntries`](https://nextjs.org/docs/app/api-reference/config/next-config-js/onDemandEntries)\n   */\n  onDemandEntries?: {\n    /** period (in ms) where the server will keep pages in the buffer */\n    maxInactiveAge?: number\n    /** number of pages that should be kept simultaneously without being disposed */\n    pagesBufferLength?: number\n  }\n\n  /** @see [`next/amp`](https://nextjs.org/docs/api-reference/next/amp) */\n  amp?: {\n    canonicalBase?: string\n  }\n\n  /**\n   * A unique identifier for a deployment that will be included in each request's query string or header.\n   */\n  deploymentId?: string\n\n  /**\n   * Deploy a Next.js application under a sub-path of a domain\n   *\n   * @see [Base path configuration](https://nextjs.org/docs/app/api-reference/config/next-config-js/basePath)\n   */\n  basePath?: string\n\n  /** @see [Customizing sass options](https://nextjs.org/docs/app/api-reference/next-config-js/sassOptions) */\n  sassOptions?: {\n    implementation?: string\n    [key: string]: any\n  }\n\n  /**\n   * Enable browser source map generation during the production build\n   *\n   * @see [Source Maps](https://nextjs.org/docs/advanced-features/source-maps)\n   */\n  productionBrowserSourceMaps?: boolean\n\n  /**\n   * Enable react profiling in production\n   *\n   */\n  reactProductionProfiling?: boolean\n\n  /**\n   * The Next.js runtime is Strict Mode-compliant.\n   *\n   * @see [React Strict Mode](https://nextjs.org/docs/app/api-reference/config/next-config-js/reactStrictMode)\n   */\n  reactStrictMode?: boolean | null\n\n  /**\n   * The maximum length of the headers that are emitted by React and added to\n   * the response.\n   *\n   * @see [React Max Headers Length](https://nextjs.org/docs/app/api-reference/config/next-config-js/reactMaxHeadersLength)\n   */\n  reactMaxHeadersLength?: number\n\n  /**\n   * Add public (in browser) runtime configuration to your app\n   *\n   * @see [Runtime configuration](https://nextjs.org/docs/pages/api-reference/config/next-config-js/runtime-configuration\n   */\n  publicRuntimeConfig?: { [key: string]: any }\n\n  /**\n   * Add server runtime configuration to your app\n   *\n   * @see [Runtime configuration](https://nextjs.org/docs/pages/api-reference/config/next-config-js/runtime-configuration\n   */\n  serverRuntimeConfig?: { [key: string]: any }\n\n  /**\n   * Next.js enables HTTP Keep-Alive by default.\n   * You may want to disable HTTP Keep-Alive for certain `fetch()` calls or globally.\n   *\n   * @see [Disabling HTTP Keep-Alive](https://nextjs.org/docs/app/api-reference/next-config-js/httpAgentOptions)\n   */\n  httpAgentOptions?: { keepAlive?: boolean }\n\n  /**\n   * Timeout after waiting to generate static pages in seconds\n   *\n   * @default 60\n   */\n  staticPageGenerationTimeout?: number\n\n  /**\n   * Add `\"crossorigin\"` attribute to generated `<script>` elements generated by `<Head />` or `<NextScript />` components\n   *\n   *\n   * @see [`crossorigin` attribute documentation](https://developer.mozilla.org/docs/Web/HTML/Attributes/crossorigin)\n   */\n  crossOrigin?: 'anonymous' | 'use-credentials'\n\n  /**\n   * Optionally enable compiler transforms\n   *\n   * @see [Supported Compiler Options](https://nextjs.org/docs/advanced-features/compiler#supported-features)\n   */\n  compiler?: {\n    reactRemoveProperties?:\n      | boolean\n      | {\n          properties?: string[]\n        }\n    relay?: {\n      src: string\n      artifactDirectory?: string\n      language?: 'typescript' | 'javascript' | 'flow'\n      eagerEsModules?: boolean\n    }\n    removeConsole?:\n      | boolean\n      | {\n          exclude?: string[]\n        }\n    styledComponents?: boolean | StyledComponentsConfig\n    emotion?: boolean | EmotionConfig\n\n    styledJsx?:\n      | boolean\n      | {\n          useLightningcss?: boolean\n        }\n\n    /**\n     * Replaces variables in your code during compile time. Each key will be\n     * replaced with the respective values.\n     */\n    define?: Record<string, string>\n\n    /**\n     * Replaces server-only (Node.js and Edge) variables in your code during compile time.\n     * Each key will be replaced with the respective values.\n     */\n    defineServer?: Record<string, string>\n\n    /**\n     * A hook function that executes after production build compilation finishes,\n     * but before running post-compilation tasks such as type checking and\n     * static page generation.\n     */\n    runAfterProductionCompile?: (metadata: {\n      /**\n       * The root directory of the project\n       */\n      projectDir: string\n      /**\n       * The build output directory (defaults to `.next`)\n       */\n      distDir: string\n    }) => Promise<void>\n  }\n\n  /**\n   * The type of build output.\n   * - `undefined`: The default build output, `.next` directory, that works with production mode `next start` or a hosting provider like Vercel\n   * - `'standalone'`: A standalone build output, `.next/standalone` directory, that only includes necessary files/dependencies. Useful for self-hosting in a Docker container.\n   * - `'export'`: An exported build output, `out` directory, that only includes static HTML/CSS/JS. Useful for self-hosting without a Node.js server.\n   * @see [Output File Tracing](https://nextjs.org/docs/advanced-features/output-file-tracing)\n   * @see [Static HTML Export](https://nextjs.org/docs/advanced-features/static-html-export)\n   */\n  output?: 'standalone' | 'export'\n\n  /**\n   * Automatically transpile and bundle dependencies from local packages (like monorepos) or from external dependencies (`node_modules`). This replaces the\n   * `next-transpile-modules` package.\n   * @see [transpilePackages](https://nextjs.org/docs/advanced-features/compiler#module-transpilation)\n   */\n  transpilePackages?: string[]\n\n  /**\n   * Options for Turbopack. Temporarily also available as `experimental.turbo` for compatibility.\n   */\n  turbopack?: TurbopackOptions\n\n  skipMiddlewareUrlNormalize?: boolean\n\n  skipTrailingSlashRedirect?: boolean\n\n  modularizeImports?: Record<\n    string,\n    {\n      transform: string | Record<string, string>\n      preventFullImport?: boolean\n      skipDefaultConversion?: boolean\n    }\n  >\n\n  /**\n   * Logging configuration. Set to `false` to disable logging.\n   */\n  logging?: LoggingConfig | false\n\n  /**\n   * period (in seconds) where the server allow to serve stale cache\n   */\n  expireTime?: number\n\n  /**\n   * Enable experimental features. Note that all experimental features are subject to breaking changes in the future.\n   */\n  experimental?: ExperimentalConfig\n\n  /**\n   * Enables the bundling of node_modules packages (externals) for pages server-side bundles.\n   * @see https://nextjs.org/docs/pages/api-reference/next-config-js/bundlePagesRouterDependencies\n   */\n  bundlePagesRouterDependencies?: boolean\n\n  /**\n   * A list of packages that should be treated as external in the server build.\n   * @see https://nextjs.org/docs/app/api-reference/next-config-js/serverExternalPackages\n   */\n  serverExternalPackages?: string[]\n\n  /**\n   * This is the repo root usually and only files above this\n   * directory are traced and included.\n   */\n  outputFileTracingRoot?: string\n\n  /**\n   * This allows manually excluding traced files if too many\n   * are included incorrectly on a per-page basis.\n   */\n  outputFileTracingExcludes?: Record<string, string[]>\n\n  /**\n   * This allows manually including traced files if some\n   * were not detected on a per-page basis.\n   */\n  outputFileTracingIncludes?: Record<string, string[]>\n\n  watchOptions?: {\n    pollIntervalMs?: number\n  }\n\n  /**\n   * User Agent of bots that can handle streaming metadata.\n   * Besides the default behavior, Next.js act differently on serving metadata to bots based on their capability.\n   *\n   * @default\n   * /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i\n   */\n  htmlLimitedBots?: RegExp\n}\n\nexport const defaultConfig = {\n  env: {},\n  webpack: null,\n  eslint: {\n    ignoreDuringBuilds: false,\n  },\n  typescript: {\n    ignoreBuildErrors: false,\n    tsconfigPath: 'tsconfig.json',\n  },\n  distDir: '.next',\n  cleanDistDir: true,\n  assetPrefix: '',\n  cacheHandler: process.env.NEXT_CACHE_HANDLER_PATH,\n  // default to 50MB limit\n  cacheMaxMemorySize: 50 * 1024 * 1024,\n  configOrigin: 'default',\n  useFileSystemPublicRoutes: true,\n  generateBuildId: () => null,\n  generateEtags: true,\n  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],\n  poweredByHeader: true,\n  compress: true,\n  images: imageConfigDefault,\n  devIndicators: {\n    position: 'bottom-left',\n  },\n  onDemandEntries: {\n    maxInactiveAge: 60 * 1000,\n    pagesBufferLength: 5,\n  },\n  amp: {\n    canonicalBase: '',\n  },\n  basePath: '',\n  sassOptions: {},\n  trailingSlash: false,\n  i18n: null,\n  productionBrowserSourceMaps: false,\n  excludeDefaultMomentLocales: true,\n  serverRuntimeConfig: {},\n  publicRuntimeConfig: {},\n  reactProductionProfiling: false,\n  reactStrictMode: null,\n  reactMaxHeadersLength: 6000,\n  httpAgentOptions: {\n    keepAlive: true,\n  },\n  logging: {},\n  compiler: {},\n  expireTime: process.env.NEXT_PRIVATE_CDN_CONSUMED_SWR_CACHE_CONTROL\n    ? undefined\n    : 31536000, // one year\n  staticPageGenerationTimeout: 60,\n  output: !!process.env.NEXT_PRIVATE_STANDALONE ? 'standalone' : undefined,\n  modularizeImports: undefined,\n  outputFileTracingRoot: process.env.NEXT_PRIVATE_OUTPUT_TRACE_ROOT || '',\n  allowedDevOrigins: undefined,\n  experimental: {\n    adapterPath: process.env.NEXT_ADAPTER_PATH || undefined,\n    useSkewCookie: false,\n    nodeMiddleware: false,\n    cacheLife: {\n      default: {\n        stale: undefined, // defaults to staleTimes.static\n        revalidate: 60 * 15, // 15 minutes\n        expire: INFINITE_CACHE,\n      },\n      seconds: {\n        stale: undefined, // defaults to staleTimes.dynamic\n        revalidate: 1, // 1 second\n        expire: 60, // 1 minute\n      },\n      minutes: {\n        stale: 60 * 5, // 5 minutes\n        revalidate: 60, // 1 minute\n        expire: 60 * 60, // 1 hour\n      },\n      hours: {\n        stale: 60 * 5, // 5 minutes\n        revalidate: 60 * 60, // 1 hour\n        expire: 60 * 60 * 24, // 1 day\n      },\n      days: {\n        stale: 60 * 5, // 5 minutes\n        revalidate: 60 * 60 * 24, // 1 day\n        expire: 60 * 60 * 24 * 7, // 1 week\n      },\n      weeks: {\n        stale: 60 * 5, // 5 minutes\n        revalidate: 60 * 60 * 24 * 7, // 1 week\n        expire: 60 * 60 * 24 * 30, // 1 month\n      },\n      max: {\n        stale: 60 * 5, // 5 minutes\n        revalidate: 60 * 60 * 24 * 30, // 1 month\n        expire: INFINITE_CACHE, // Unbounded.\n      },\n    },\n    cacheHandlers: {\n      default: process.env.NEXT_DEFAULT_CACHE_HANDLER_PATH,\n      remote: process.env.NEXT_REMOTE_CACHE_HANDLER_PATH,\n      static: process.env.NEXT_STATIC_CACHE_HANDLER_PATH,\n    },\n    cssChunking: true,\n    multiZoneDraftMode: false,\n    appNavFailHandling: false,\n    prerenderEarlyExit: true,\n    serverMinification: true,\n    // Will default to dynamicIO value.\n    enablePrerenderSourceMaps: undefined,\n    serverSourceMaps: false,\n    linkNoTouchStart: false,\n    caseSensitiveRoutes: false,\n    clientSegmentCache:\n      // TODO: Remove once we've made clientSegmentCache the default. We're\n      // piggybacking on the PPR test flag, instead of introducing a separate\n      // CI run.\n      //\n      // If we're testing, and the `__NEXT_EXPERIMENTAL_PPR` environment\n      // variable has been set to `true`, enable the experimental\n      // clientSegmentCache feature so long as it wasn't explicitly disabled in\n      // the config.\n      !!(\n        process.env.__NEXT_TEST_MODE &&\n        process.env.__NEXT_EXPERIMENTAL_PPR === 'true'\n      ),\n    dynamicOnHover: false,\n    appDocumentPreloading: undefined,\n    preloadEntriesOnStart: true,\n    clientRouterFilter: true,\n    clientRouterFilterRedirects: false,\n    fetchCacheKeyPrefix: '',\n    middlewarePrefetch: 'flexible',\n    optimisticClientCache: true,\n    manualClientBasePath: false,\n    cpus: Math.max(\n      1,\n      (Number(process.env.CIRCLE_NODE_TOTAL) ||\n        (os.cpus() || { length: 1 }).length) - 1\n    ),\n    memoryBasedWorkersCount: false,\n    imgOptConcurrency: null,\n    imgOptTimeoutInSeconds: 7,\n    imgOptMaxInputPixels: 268_402_689, // https://sharp.pixelplumbing.com/api-constructor#:~:text=%5Boptions.limitInputPixels%5D\n    imgOptSequentialRead: null,\n    isrFlushToDisk: true,\n    workerThreads: false,\n    proxyTimeout: undefined,\n    optimizeCss: false,\n    nextScriptWorkers: false,\n    scrollRestoration: false,\n    externalDir: false,\n    disableOptimizedLoading: false,\n    gzipSize: true,\n    craCompat: false,\n    esmExternals: true,\n    fullySpecified: false,\n    swcTraceProfiling: false,\n    forceSwcTransforms: false,\n    swcPlugins: undefined,\n    largePageDataBytes: 128 * 1000, // 128KB by default\n    disablePostcssPresetEnv: undefined,\n    amp: undefined,\n    urlImports: undefined,\n    turbo: undefined,\n    typedRoutes: false,\n    typedEnv: false,\n    clientTraceMetadata: undefined,\n    parallelServerCompiles: false,\n    parallelServerBuildTraces: false,\n    ppr:\n      // TODO: remove once we've made PPR default\n      // If we're testing, and the `__NEXT_EXPERIMENTAL_PPR` environment variable\n      // has been set to `true`, enable the experimental PPR feature so long as it\n      // wasn't explicitly disabled in the config.\n      !!(\n        process.env.__NEXT_TEST_MODE &&\n        process.env.__NEXT_EXPERIMENTAL_PPR === 'true'\n      ),\n    authInterrupts: false,\n    webpackBuildWorker: undefined,\n    webpackMemoryOptimizations: false,\n    optimizeServerReact: true,\n    viewTransition: false,\n    routerBFCache: false,\n    removeUncaughtErrorAndRejectionListeners: false,\n    validateRSCRequestHeaders: !!(\n      process.env.__NEXT_TEST_MODE || !isStableBuild()\n    ),\n    staleTimes: {\n      dynamic: 0,\n      static: 300,\n    },\n    allowDevelopmentBuild: undefined,\n    reactCompiler: undefined,\n    staticGenerationRetryCount: undefined,\n    serverComponentsHmrCache: true,\n    staticGenerationMaxConcurrency: 8,\n    staticGenerationMinPagesPerWorker: 25,\n    dynamicIO:\n      // TODO: remove once we've made dynamicIO the default\n      // If we're testing, and the `__NEXT_EXPERIMENTAL_CACHE_COMPONENTS` environment\n      // variable has been set to `true`, enable the experimental dynamicIO feature so long as it\n      // wasn't explicitly disabled in the config.\n      !!(\n        process.env.__NEXT_TEST_MODE &&\n        process.env.__NEXT_EXPERIMENTAL_CACHE_COMPONENTS === 'true'\n      ),\n    inlineCss: false,\n    useCache: undefined,\n    slowModuleDetection: undefined,\n    globalNotFound: false,\n    devtoolNewPanelUI: process.env.__NEXT_DEVTOOL_NEW_PANEL_UI === 'true',\n    devtoolSegmentExplorer: process.env.__NEXT_DEVTOOL_NEW_PANEL_UI === 'true',\n    browserDebugInfoInTerminal: false,\n    optimizeRouterScrolling: false,\n  },\n  htmlLimitedBots: undefined,\n  bundlePagesRouterDependencies: false,\n} satisfies NextConfig\n\nexport async function normalizeConfig(phase: string, config: any) {\n  if (typeof config === 'function') {\n    config = config(phase, { defaultConfig })\n  }\n  // Support `new Promise` and `async () =>` as return values of the config export\n  return await config\n}\n"], "names": ["os", "imageConfigDefault", "INFINITE_CACHE", "isStableBuild", "defaultConfig", "env", "webpack", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "cache<PERSON><PERSON><PERSON>", "process", "NEXT_CACHE_HANDLER_PATH", "cacheMaxMemorySize", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "poweredByHeader", "compress", "images", "devIndicators", "position", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactProductionProfiling", "reactStrictMode", "reactMaxHeadersLength", "httpAgentOptions", "keepAlive", "logging", "compiler", "expireTime", "NEXT_PRIVATE_CDN_CONSUMED_SWR_CACHE_CONTROL", "undefined", "staticPageGenerationTimeout", "output", "NEXT_PRIVATE_STANDALONE", "modularizeImports", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "experimental", "adapterPath", "NEXT_ADAPTER_PATH", "useSkewCookie", "nodeMiddleware", "cacheLife", "default", "stale", "revalidate", "expire", "seconds", "minutes", "hours", "days", "weeks", "max", "cacheHandlers", "NEXT_DEFAULT_CACHE_HANDLER_PATH", "remote", "NEXT_REMOTE_CACHE_HANDLER_PATH", "static", "NEXT_STATIC_CACHE_HANDLER_PATH", "cssChunking", "multiZoneDraftMode", "appNavFailHandling", "prerenderEarlyExit", "serverMinification", "enablePrerenderSourceMaps", "serverSourceMaps", "linkNoTouchStart", "caseSensitiveRoutes", "clientSegmentCache", "__NEXT_TEST_MODE", "__NEXT_EXPERIMENTAL_PPR", "dynamicOnHover", "appDocumentPreloading", "preloadEntriesOnStart", "clientRouterFilter", "clientRouterFilterRedirects", "fetchCacheKeyPrefix", "middlewarePrefetch", "optimisticClientCache", "manualClientBasePath", "cpus", "Math", "Number", "CIRCLE_NODE_TOTAL", "length", "memoryBasedWorkersCount", "imgOptConcurrency", "imgOptTimeoutInSeconds", "imgOptMaxInputPixels", "imgOptSequentialRead", "isrFlushToDisk", "workerThreads", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "craCompat", "esmExternals", "fullySpecified", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "turbo", "typedRoutes", "typedEnv", "clientTraceMetadata", "parallelServerCompiles", "parallelServerBuildTraces", "ppr", "authInterrupts", "webpackBuildWorker", "webpackMemoryOptimizations", "optimizeServerReact", "viewTransition", "routerBFCache", "removeUncaughtErrorAndRejectionListeners", "validateRSCRequestHeaders", "staleTimes", "dynamic", "allowDevelopmentBuild", "reactCompiler", "staticGenerationRetryCount", "serverComponentsHmrCache", "staticGenerationMaxConcurrency", "staticGenerationMinPagesPerWorker", "dynamicIO", "__NEXT_EXPERIMENTAL_CACHE_COMPONENTS", "inlineCss", "useCache", "slowModuleDetection", "globalNotFound", "devtoolNewPanelUI", "__NEXT_DEVTOOL_NEW_PANEL_UI", "devtoolSegmentExplorer", "browserDebugInfoInTerminal", "optimizeRouterScrolling", "htmlLimitedBots", "bundlePagesRouterDependencies", "normalizeConfig", "phase", "config"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAGnB,SAASC,kBAAkB,QAAQ,6BAA4B;AAW/D,SAASC,cAAc,QAAQ,mBAAkB;AAOjD,SAASC,aAAa,QAAQ,4BAA2B;AAgvCzD,OAAO,MAAMC,gBAAgB;IAC3BC,KAAK,CAAC;IACNC,SAAS;IACTC,QAAQ;QACNC,oBAAoB;IACtB;IACAC,YAAY;QACVC,mBAAmB;QACnBC,cAAc;IAChB;IACAC,SAAS;IACTC,cAAc;IACdC,aAAa;IACbC,cAAcC,QAAQX,GAAG,CAACY,uBAAuB;IACjD,wBAAwB;IACxBC,oBAAoB,KAAK,OAAO;IAChCC,cAAc;IACdC,2BAA2B;IAC3BC,iBAAiB,IAAM;IACvBC,eAAe;IACfC,gBAAgB;QAAC;QAAO;QAAM;QAAO;KAAK;IAC1CC,iBAAiB;IACjBC,UAAU;IACVC,QAAQzB;IACR0B,eAAe;QACbC,UAAU;IACZ;IACAC,iBAAiB;QACfC,gBAAgB,KAAK;QACrBC,mBAAmB;IACrB;IACAC,KAAK;QACHC,eAAe;IACjB;IACAC,UAAU;IACVC,aAAa,CAAC;IACdC,eAAe;IACfC,MAAM;IACNC,6BAA6B;IAC7BC,6BAA6B;IAC7BC,qBAAqB,CAAC;IACtBC,qBAAqB,CAAC;IACtBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,uBAAuB;IACvBC,kBAAkB;QAChBC,WAAW;IACb;IACAC,SAAS,CAAC;IACVC,UAAU,CAAC;IACXC,YAAYjC,QAAQX,GAAG,CAAC6C,2CAA2C,GAC/DC,YACA;IACJC,6BAA6B;IAC7BC,QAAQ,CAAC,CAACrC,QAAQX,GAAG,CAACiD,uBAAuB,GAAG,eAAeH;IAC/DI,mBAAmBJ;IACnBK,uBAAuBxC,QAAQX,GAAG,CAACoD,8BAA8B,IAAI;IACrEC,mBAAmBP;IACnBQ,cAAc;QACZC,aAAa5C,QAAQX,GAAG,CAACwD,iBAAiB,IAAIV;QAC9CW,eAAe;QACfC,gBAAgB;QAChBC,WAAW;YACTC,SAAS;gBACPC,OAAOf;gBACPgB,YAAY,KAAK;gBACjBC,QAAQlE;YACV;YACAmE,SAAS;gBACPH,OAAOf;gBACPgB,YAAY;gBACZC,QAAQ;YACV;YACAE,SAAS;gBACPJ,OAAO,KAAK;gBACZC,YAAY;gBACZC,QAAQ,KAAK;YACf;YACAG,OAAO;gBACLL,OAAO,KAAK;gBACZC,YAAY,KAAK;gBACjBC,QAAQ,KAAK,KAAK;YACpB;YACAI,MAAM;gBACJN,OAAO,KAAK;gBACZC,YAAY,KAAK,KAAK;gBACtBC,QAAQ,KAAK,KAAK,KAAK;YACzB;YACAK,OAAO;gBACLP,OAAO,KAAK;gBACZC,YAAY,KAAK,KAAK,KAAK;gBAC3BC,QAAQ,KAAK,KAAK,KAAK;YACzB;YACAM,KAAK;gBACHR,OAAO,KAAK;gBACZC,YAAY,KAAK,KAAK,KAAK;gBAC3BC,QAAQlE;YACV;QACF;QACAyE,eAAe;YACbV,SAASjD,QAAQX,GAAG,CAACuE,+BAA+B;YACpDC,QAAQ7D,QAAQX,GAAG,CAACyE,8BAA8B;YAClDC,QAAQ/D,QAAQX,GAAG,CAAC2E,8BAA8B;QACpD;QACAC,aAAa;QACbC,oBAAoB;QACpBC,oBAAoB;QACpBC,oBAAoB;QACpBC,oBAAoB;QACpB,mCAAmC;QACnCC,2BAA2BnC;QAC3BoC,kBAAkB;QAClBC,kBAAkB;QAClBC,qBAAqB;QACrBC,oBACE,qEAAqE;QACrE,uEAAuE;QACvE,UAAU;QACV,EAAE;QACF,kEAAkE;QAClE,2DAA2D;QAC3D,yEAAyE;QACzE,cAAc;QACd,CAAC,CACC1E,CAAAA,QAAQX,GAAG,CAACsF,gBAAgB,IAC5B3E,QAAQX,GAAG,CAACuF,uBAAuB,KAAK,MAAK;QAEjDC,gBAAgB;QAChBC,uBAAuB3C;QACvB4C,uBAAuB;QACvBC,oBAAoB;QACpBC,6BAA6B;QAC7BC,qBAAqB;QACrBC,oBAAoB;QACpBC,uBAAuB;QACvBC,sBAAsB;QACtBC,MAAMC,KAAK7B,GAAG,CACZ,GACA,AAAC8B,CAAAA,OAAOxF,QAAQX,GAAG,CAACoG,iBAAiB,KACnC,AAACzG,CAAAA,GAAGsG,IAAI,MAAM;YAAEI,QAAQ;QAAE,CAAA,EAAGA,MAAM,AAAD,IAAK;QAE3CC,yBAAyB;QACzBC,mBAAmB;QACnBC,wBAAwB;QACxBC,sBAAsB;QACtBC,sBAAsB;QACtBC,gBAAgB;QAChBC,eAAe;QACfC,cAAc/D;QACdgE,aAAa;QACbC,mBAAmB;QACnBC,mBAAmB;QACnBC,aAAa;QACbC,yBAAyB;QACzBC,UAAU;QACVC,WAAW;QACXC,cAAc;QACdC,gBAAgB;QAChBC,mBAAmB;QACnBC,oBAAoB;QACpBC,YAAY3E;QACZ4E,oBAAoB,MAAM;QAC1BC,yBAAyB7E;QACzBnB,KAAKmB;QACL8E,YAAY9E;QACZ+E,OAAO/E;QACPgF,aAAa;QACbC,UAAU;QACVC,qBAAqBlF;QACrBmF,wBAAwB;QACxBC,2BAA2B;QAC3BC,KACE,2CAA2C;QAC3C,2EAA2E;QAC3E,4EAA4E;QAC5E,4CAA4C;QAC5C,CAAC,CACCxH,CAAAA,QAAQX,GAAG,CAACsF,gBAAgB,IAC5B3E,QAAQX,GAAG,CAACuF,uBAAuB,KAAK,MAAK;QAEjD6C,gBAAgB;QAChBC,oBAAoBvF;QACpBwF,4BAA4B;QAC5BC,qBAAqB;QACrBC,gBAAgB;QAChBC,eAAe;QACfC,0CAA0C;QAC1CC,2BAA2B,CAAC,CAC1BhI,CAAAA,QAAQX,GAAG,CAACsF,gBAAgB,IAAI,CAACxF,eAAc;QAEjD8I,YAAY;YACVC,SAAS;YACTnE,QAAQ;QACV;QACAoE,uBAAuBhG;QACvBiG,eAAejG;QACfkG,4BAA4BlG;QAC5BmG,0BAA0B;QAC1BC,gCAAgC;QAChCC,mCAAmC;QACnCC,WACE,qDAAqD;QACrD,+EAA+E;QAC/E,2FAA2F;QAC3F,4CAA4C;QAC5C,CAAC,CACCzI,CAAAA,QAAQX,GAAG,CAACsF,gBAAgB,IAC5B3E,QAAQX,GAAG,CAACqJ,oCAAoC,KAAK,MAAK;QAE9DC,WAAW;QACXC,UAAUzG;QACV0G,qBAAqB1G;QACrB2G,gBAAgB;QAChBC,mBAAmB/I,QAAQX,GAAG,CAAC2J,2BAA2B,KAAK;QAC/DC,wBAAwBjJ,QAAQX,GAAG,CAAC2J,2BAA2B,KAAK;QACpEE,4BAA4B;QAC5BC,yBAAyB;IAC3B;IACAC,iBAAiBjH;IACjBkH,+BAA+B;AACjC,EAAsB;AAEtB,OAAO,eAAeC,gBAAgBC,KAAa,EAAEC,MAAW;IAC9D,IAAI,OAAOA,WAAW,YAAY;QAChCA,SAASA,OAAOD,OAAO;YAAEnK;QAAc;IACzC;IACA,gFAAgF;IAChF,OAAO,MAAMoK;AACf", "ignoreList": [0]}