{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/index.ts"], "sourcesContent": ["import { LightningCssLoader } from './loader'\n\nexport { LightningCssMinifyPlugin } from './minify'\nexport default LightningCssLoader\n"], "names": ["Lightning<PERSON>s<PERSON><PERSON>der", "LightningCssMinifyPlugin"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,WAAU;AAE7C,SAASC,wBAAwB,QAAQ,WAAU;AACnD,eAAeD,mBAAkB", "ignoreList": [0]}