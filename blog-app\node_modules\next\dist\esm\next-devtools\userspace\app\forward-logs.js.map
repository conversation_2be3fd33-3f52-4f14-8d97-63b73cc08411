{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/forward-logs.ts"], "sourcesContent": ["import { configure } from 'next/dist/compiled/safe-stable-stringify'\nimport {\n  getOwnerStack,\n  setOwnerStackIfAvailable,\n} from './errors/stitched-error'\nimport { getErrorSource } from '../../../shared/lib/error-source'\nimport {\n  getTerminalLoggingConfig,\n  getIsTerminalLoggingEnabled,\n} from './terminal-logging-config'\nimport {\n  type ConsoleEntry,\n  type ConsoleErrorEntry,\n  type FormattedErrorEntry,\n  type ClientLogEntry,\n  type LogMethod,\n  patchConsoleMethod,\n  UNDEFINED_MARKER,\n} from '../../shared/forward-logs-shared'\n\nconst terminalLoggingConfig = getTerminalLoggingConfig()\nexport const PROMISE_MARKER = 'Promise {}'\nexport const UNAVAILABLE_MARKER = '[Unable to view]'\n\nconst maximumDepth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.depthLimit\n    ? terminalLoggingConfig.depthLimit\n    : 5\nconst maximumBreadth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.edgeLimit\n    ? terminalLoggingConfig.edgeLimit\n    : 100\n\nconst stringify = configure({\n  maximumDepth,\n  maximumBreadth,\n})\n\nexport const isTerminalLoggingEnabled = getIsTerminalLoggingEnabled()\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n  'trace',\n]\n/**\n * allows us to:\n * - revive the undefined log in the server as it would look in the browser\n * - not read/attempt to serialize promises (next will console error if you do that, and will cause this program to infinitely recurse)\n * - if we read a proxy that throws (no way to detect if something is a proxy), explain to the user we can't read this data\n */\nexport function preLogSerializationClone<T>(\n  value: T,\n  seen = new WeakMap()\n): any {\n  if (value === undefined) return UNDEFINED_MARKER\n  if (value === null || typeof value !== 'object') return value\n  if (seen.has(value as object)) return seen.get(value as object)\n\n  try {\n    Object.keys(value as object)\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  try {\n    if (typeof (value as any).then === 'function') return PROMISE_MARKER\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  if (Array.isArray(value)) {\n    const out: any[] = []\n    seen.set(value, out)\n    for (const item of value) {\n      try {\n        out.push(preLogSerializationClone(item, seen))\n      } catch {\n        out.push(UNAVAILABLE_MARKER)\n      }\n    }\n    return out\n  }\n\n  const proto = Object.getPrototypeOf(value)\n  if (proto === Object.prototype || proto === null) {\n    const out: Record<string, unknown> = {}\n    seen.set(value as object, out)\n    for (const key of Object.keys(value as object)) {\n      try {\n        out[key] = preLogSerializationClone((value as any)[key], seen)\n      } catch {\n        out[key] = UNAVAILABLE_MARKER\n      }\n    }\n    return out\n  }\n\n  return Object.prototype.toString.call(value)\n}\n\n// only safe if passed safeClone data\nexport const logStringify = (data: unknown): string => {\n  try {\n    const result = stringify(data)\n    return result ?? `\"${UNAVAILABLE_MARKER}\"`\n  } catch {\n    return `\"${UNAVAILABLE_MARKER}\"`\n  }\n}\n\nconst afterThisFrame = (cb: () => void) => {\n  let timeout: ReturnType<typeof setTimeout> | undefined\n\n  const rafId = requestAnimationFrame(() => {\n    timeout = setTimeout(() => {\n      cb()\n    })\n  })\n\n  return () => {\n    cancelAnimationFrame(rafId)\n    clearTimeout(timeout)\n  }\n}\n\nlet isPatched = false\n\nconst serializeEntries = (entries: Array<ClientLogEntry>) =>\n  entries.map((clientEntry) => {\n    switch (clientEntry.kind) {\n      case 'any-logged-error':\n      case 'console': {\n        return {\n          ...clientEntry,\n          args: clientEntry.args.map(stringifyUserArg),\n        }\n      }\n      case 'formatted-error': {\n        return clientEntry\n      }\n      default: {\n        return null!\n      }\n    }\n  })\n\nexport const logQueue: {\n  entries: Array<ClientLogEntry>\n  onSocketReady: (socket: WebSocket) => void\n  flushScheduled: boolean\n  socket: WebSocket | null\n  cancelFlush: (() => void) | null\n  sourceType?: 'server' | 'edge-server'\n  router: 'app' | 'pages' | null\n  scheduleLogSend: (entry: ClientLogEntry) => void\n} = {\n  entries: [],\n  flushScheduled: false,\n  cancelFlush: null,\n  socket: null,\n  sourceType: undefined,\n  router: null,\n  scheduleLogSend: (entry: ClientLogEntry) => {\n    logQueue.entries.push(entry)\n    if (logQueue.flushScheduled) {\n      return\n    }\n    // safe to deref and use in setTimeout closure since we cancel on new socket\n    const socket = logQueue.socket\n    if (!socket) {\n      return\n    }\n\n    // we probably dont need this\n    logQueue.flushScheduled = true\n\n    // non blocking log flush, runs at most once per frame\n    logQueue.cancelFlush = afterThisFrame(() => {\n      logQueue.flushScheduled = false\n\n      // just incase\n      try {\n        const payload = JSON.stringify({\n          event: 'browser-logs',\n          entries: serializeEntries(logQueue.entries),\n          router: logQueue.router,\n          // needed for source mapping, we just assign the sourceType from the last error for the whole batch\n          sourceType: logQueue.sourceType,\n        })\n\n        socket.send(payload)\n        logQueue.entries = []\n        logQueue.sourceType = undefined\n      } catch {\n        // error (make sure u don't infinite loop)\n        /* noop */\n      }\n    })\n  },\n  onSocketReady: (socket: WebSocket) => {\n    if (socket.readyState !== WebSocket.OPEN) {\n      // invariant\n      return\n    }\n\n    // incase an existing timeout was going to run with a stale socket\n    logQueue.cancelFlush?.()\n    logQueue.socket = socket\n    try {\n      const payload = JSON.stringify({\n        event: 'browser-logs',\n        entries: serializeEntries(logQueue.entries),\n        router: logQueue.router,\n        sourceType: logQueue.sourceType,\n      })\n\n      socket.send(payload)\n      logQueue.entries = []\n      logQueue.sourceType = undefined\n    } catch {\n      /** noop just incase */\n    }\n  },\n}\n\nconst stringifyUserArg = (\n  arg:\n    | {\n        kind: 'arg'\n        data: unknown\n      }\n    | {\n        kind: 'formatted-error-arg'\n      }\n) => {\n  if (arg.kind !== 'arg') {\n    return arg\n  }\n  return {\n    ...arg,\n    data: logStringify(arg.data),\n  }\n}\n\nconst createErrorArg = (error: Error) => {\n  const stack = stackWithOwners(error)\n  return {\n    kind: 'formatted-error-arg' as const,\n    prefix: error.message ? `${error.name}: ${error.message}` : `${error.name}`,\n    stack,\n  }\n}\n\nconst createLogEntry = (level: LogMethod, args: any[]) => {\n  // do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n  // error capture stack trace maybe\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n') // this is probably ignored anyways\n  const entry: ConsoleEntry<unknown> = {\n    kind: 'console',\n    consoleMethodStack: cleanStack ?? null, // depending on browser we might not have stack\n    method: level,\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nexport const forwardErrorLog = (args: any[]) => {\n  const errorObjects = args.filter((arg) => arg instanceof Error)\n  const first = errorObjects.at(0)\n  if (first) {\n    const source = getErrorSource(first)\n    if (source) {\n      logQueue.sourceType = source\n    }\n  }\n  /**\n   * browser shows stack regardless of type of data passed to console.error, so we should do the same\n   *\n   * do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n   */\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n')\n\n  const entry: ConsoleErrorEntry<unknown> = {\n    kind: 'any-logged-error',\n    method: 'error',\n    consoleErrorStack: cleanStack ?? '',\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUncaughtErrorEntry = (\n  errorName: string,\n  errorMessage: string,\n  fullStack: string\n) => {\n  const entry: FormattedErrorEntry = {\n    kind: 'formatted-error',\n    prefix: `Uncaught ${errorName}: ${errorMessage}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst stackWithOwners = (error: Error) => {\n  let ownerStack = ''\n  setOwnerStackIfAvailable(error)\n  ownerStack = getOwnerStack(error) || ''\n  const stack = (error.stack || '') + ownerStack\n  return stack\n}\n\nexport function logUnhandledRejection(reason: unknown) {\n  if (reason instanceof Error) {\n    createUnhandledRejectionErrorEntry(reason, stackWithOwners(reason))\n    return\n  }\n  createUnhandledRejectionNonErrorEntry(reason)\n}\n\nconst createUnhandledRejectionErrorEntry = (\n  error: Error,\n  fullStack: string\n) => {\n  const source = getErrorSource(error)\n  if (source) {\n    logQueue.sourceType = source\n  }\n\n  const entry: ClientLogEntry = {\n    kind: 'formatted-error',\n    prefix: `⨯ unhandledRejection: ${error.name}: ${error.message}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUnhandledRejectionNonErrorEntry = (reason: unknown) => {\n  const entry: ClientLogEntry = {\n    kind: 'any-logged-error',\n    // we can't access the stack since the event is dispatched async and creating an inline error would be meaningless\n    consoleErrorStack: '',\n    method: 'error',\n    args: [\n      {\n        kind: 'arg',\n        data: `⨯ unhandledRejection:`,\n        isRejectionMessage: true,\n      },\n      {\n        kind: 'arg',\n        data: preLogSerializationClone(reason),\n      },\n    ],\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst isHMR = (args: any[]) => {\n  const firstArg = args[0]\n  if (typeof firstArg !== 'string') {\n    return false\n  }\n  if (firstArg.startsWith('[Fast Refresh]')) {\n    return true\n  }\n\n  if (firstArg.startsWith('[HMR]')) {\n    return true\n  }\n\n  return false\n}\n\nconst isIgnoredLog = (args: any[]) => {\n  if (args.length < 3) {\n    return false\n  }\n\n  const [format, styles, label] = args\n\n  if (\n    typeof format !== 'string' ||\n    typeof styles !== 'string' ||\n    typeof label !== 'string'\n  ) {\n    return false\n  }\n\n  // kinda hacky, we should define a common format for these strings so we can safely ignore\n  return format.startsWith('%c%s%c') && styles.includes('background:')\n}\n\nexport function forwardUnhandledError(error: Error) {\n  createUncaughtErrorEntry(error.name, error.message, stackWithOwners(error))\n}\n\n// TODO: this router check is brittle, we need to update based on the current router the user is using\nexport const initializeDebugLogForwarding = (router: 'app' | 'pages'): void => {\n  // probably don't need this\n  if (isPatched) {\n    return\n  }\n  // TODO(rob): why does this break rendering on server, important to know incase the same bug appears in browser\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  // better to be safe than sorry\n  try {\n    methods.forEach((method) =>\n      patchConsoleMethod(method, (_, ...args) => {\n        if (isHMR(args)) {\n          return\n        }\n        if (isIgnoredLog(args)) {\n          return\n        }\n        createLogEntry(method, args)\n      })\n    )\n  } catch {}\n  logQueue.router = router\n  isPatched = true\n}\n"], "names": ["configure", "getOwnerStack", "setOwnerStackIfAvailable", "getErrorSource", "getTerminalLoggingConfig", "getIsTerminalLoggingEnabled", "patchConsoleMethod", "UNDEFINED_MARKER", "terminalLoggingConfig", "PROMISE_MARKER", "UNAVAILABLE_MARKER", "maximumDepth", "depthLimit", "maximumBreadth", "edgeLimit", "stringify", "isTerminalLoggingEnabled", "methods", "preLogSerializationClone", "value", "seen", "WeakMap", "undefined", "has", "get", "Object", "keys", "then", "Array", "isArray", "out", "set", "item", "push", "proto", "getPrototypeOf", "prototype", "key", "toString", "call", "logStringify", "data", "result", "afterT<PERSON><PERSON><PERSON>e", "cb", "timeout", "rafId", "requestAnimationFrame", "setTimeout", "cancelAnimationFrame", "clearTimeout", "isPatched", "serializeEntries", "entries", "map", "clientEntry", "kind", "args", "stringifyUserArg", "logQueue", "flushScheduled", "cancelFlush", "socket", "sourceType", "router", "scheduleLogSend", "entry", "payload", "JSON", "event", "send", "onSocketReady", "readyState", "WebSocket", "OPEN", "arg", "createErrorArg", "error", "stack", "stackWithOwners", "prefix", "message", "name", "createLogEntry", "level", "Error", "stackLines", "split", "cleanStack", "slice", "join", "consoleMethodStack", "method", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorObjects", "filter", "first", "at", "source", "consoleErrorStack", "createUncaughtErrorEntry", "errorName", "errorMessage", "fullStack", "ownerStack", "logUnhandledRejection", "reason", "createUnhandledRejectionErrorEntry", "createUnhandledRejectionNonErrorEntry", "isRejectionMessage", "isHMR", "firstArg", "startsWith", "isIgnoredLog", "length", "format", "styles", "label", "includes", "forwardUnhandledError", "initializeDebugLogForwarding", "window", "for<PERSON>ach", "_"], "mappings": "AAAA,SAASA,SAAS,QAAQ,2CAA0C;AACpE,SACEC,aAAa,EACbC,wBAAwB,QACnB,0BAAyB;AAChC,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,wBAAwB,EACxBC,2BAA2B,QACtB,4BAA2B;AAClC,SAMEC,kBAAkB,EAClBC,gBAAgB,QACX,mCAAkC;AAEzC,MAAMC,wBAAwBJ;AAC9B,OAAO,MAAMK,iBAAiB,aAAY;AAC1C,OAAO,MAAMC,qBAAqB,mBAAkB;AAEpD,MAAMC,eACJ,OAAOH,0BAA0B,YAAYA,sBAAsBI,UAAU,GACzEJ,sBAAsBI,UAAU,GAChC;AACN,MAAMC,iBACJ,OAAOL,0BAA0B,YAAYA,sBAAsBM,SAAS,GACxEN,sBAAsBM,SAAS,GAC/B;AAEN,MAAMC,YAAYf,UAAU;IAC1BW;IACAE;AACF;AAEA,OAAO,MAAMG,2BAA2BX,8BAA6B;AAErE,MAAMY,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;;;;CAKC,GACD,OAAO,SAASC,yBACdC,KAAQ,EACRC,IAAoB;IAApBA,IAAAA,iBAAAA,OAAO,IAAIC;IAEX,IAAIF,UAAUG,WAAW,OAAOf;IAChC,IAAIY,UAAU,QAAQ,OAAOA,UAAU,UAAU,OAAOA;IACxD,IAAIC,KAAKG,GAAG,CAACJ,QAAkB,OAAOC,KAAKI,GAAG,CAACL;IAE/C,IAAI;QACFM,OAAOC,IAAI,CAACP;IACd,EAAE,UAAM;QACN,OAAOT;IACT;IAEA,IAAI;QACF,IAAI,OAAO,AAACS,MAAcQ,IAAI,KAAK,YAAY,OAAOlB;IACxD,EAAE,UAAM;QACN,OAAOC;IACT;IAEA,IAAIkB,MAAMC,OAAO,CAACV,QAAQ;QACxB,MAAMW,MAAa,EAAE;QACrBV,KAAKW,GAAG,CAACZ,OAAOW;QAChB,KAAK,MAAME,QAAQb,MAAO;YACxB,IAAI;gBACFW,IAAIG,IAAI,CAACf,yBAAyBc,MAAMZ;YAC1C,EAAE,UAAM;gBACNU,IAAIG,IAAI,CAACvB;YACX;QACF;QACA,OAAOoB;IACT;IAEA,MAAMI,QAAQT,OAAOU,cAAc,CAAChB;IACpC,IAAIe,UAAUT,OAAOW,SAAS,IAAIF,UAAU,MAAM;QAChD,MAAMJ,MAA+B,CAAC;QACtCV,KAAKW,GAAG,CAACZ,OAAiBW;QAC1B,KAAK,MAAMO,OAAOZ,OAAOC,IAAI,CAACP,OAAkB;YAC9C,IAAI;gBACFW,GAAG,CAACO,IAAI,GAAGnB,yBAAyB,AAACC,KAAa,CAACkB,IAAI,EAAEjB;YAC3D,EAAE,UAAM;gBACNU,GAAG,CAACO,IAAI,GAAG3B;YACb;QACF;QACA,OAAOoB;IACT;IAEA,OAAOL,OAAOW,SAAS,CAACE,QAAQ,CAACC,IAAI,CAACpB;AACxC;AAEA,qCAAqC;AACrC,OAAO,MAAMqB,eAAe,CAACC;IAC3B,IAAI;QACF,MAAMC,SAAS3B,UAAU0B;QACzB,OAAOC,iBAAAA,SAAU,AAAC,MAAGhC,qBAAmB;IAC1C,EAAE,UAAM;QACN,OAAO,AAAC,MAAGA,qBAAmB;IAChC;AACF,EAAC;AAED,MAAMiC,iBAAiB,CAACC;IACtB,IAAIC;IAEJ,MAAMC,QAAQC,sBAAsB;QAClCF,UAAUG,WAAW;YACnBJ;QACF;IACF;IAEA,OAAO;QACLK,qBAAqBH;QACrBI,aAAaL;IACf;AACF;AAEA,IAAIM,YAAY;AAEhB,MAAMC,mBAAmB,CAACC,UACxBA,QAAQC,GAAG,CAAC,CAACC;QACX,OAAQA,YAAYC,IAAI;YACtB,KAAK;YACL,KAAK;gBAAW;oBACd,OAAO;wBACL,GAAGD,WAAW;wBACdE,MAAMF,YAAYE,IAAI,CAACH,GAAG,CAACI;oBAC7B;gBACF;YACA,KAAK;gBAAmB;oBACtB,OAAOH;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;AAEF,OAAO,MAAMI,WAST;IACFN,SAAS,EAAE;IACXO,gBAAgB;IAChBC,aAAa;IACbC,QAAQ;IACRC,YAAYzC;IACZ0C,QAAQ;IACRC,iBAAiB,CAACC;QAChBP,SAASN,OAAO,CAACpB,IAAI,CAACiC;QACtB,IAAIP,SAASC,cAAc,EAAE;YAC3B;QACF;QACA,4EAA4E;QAC5E,MAAME,SAASH,SAASG,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,6BAA6B;QAC7BH,SAASC,cAAc,GAAG;QAE1B,sDAAsD;QACtDD,SAASE,WAAW,GAAGlB,eAAe;YACpCgB,SAASC,cAAc,GAAG;YAE1B,cAAc;YACd,IAAI;gBACF,MAAMO,UAAUC,KAAKrD,SAAS,CAAC;oBAC7BsD,OAAO;oBACPhB,SAASD,iBAAiBO,SAASN,OAAO;oBAC1CW,QAAQL,SAASK,MAAM;oBACvB,mGAAmG;oBACnGD,YAAYJ,SAASI,UAAU;gBACjC;gBAEAD,OAAOQ,IAAI,CAACH;gBACZR,SAASN,OAAO,GAAG,EAAE;gBACrBM,SAASI,UAAU,GAAGzC;YACxB,EAAE,UAAM;YACN,0CAA0C;YAC1C,QAAQ,GACV;QACF;IACF;IACAiD,eAAe,CAACT;QACd,IAAIA,OAAOU,UAAU,KAAKC,UAAUC,IAAI,EAAE;YACxC,YAAY;YACZ;QACF;QAEA,kEAAkE;QAClEf,SAASE,WAAW,oBAApBF,SAASE,WAAW,MAApBF;QACAA,SAASG,MAAM,GAAGA;QAClB,IAAI;YACF,MAAMK,UAAUC,KAAKrD,SAAS,CAAC;gBAC7BsD,OAAO;gBACPhB,SAASD,iBAAiBO,SAASN,OAAO;gBAC1CW,QAAQL,SAASK,MAAM;gBACvBD,YAAYJ,SAASI,UAAU;YACjC;YAEAD,OAAOQ,IAAI,CAACH;YACZR,SAASN,OAAO,GAAG,EAAE;YACrBM,SAASI,UAAU,GAAGzC;QACxB,EAAE,UAAM;QACN,qBAAqB,GACvB;IACF;AACF,EAAC;AAED,MAAMoC,mBAAmB,CACvBiB;IASA,IAAIA,IAAInB,IAAI,KAAK,OAAO;QACtB,OAAOmB;IACT;IACA,OAAO;QACL,GAAGA,GAAG;QACNlC,MAAMD,aAAamC,IAAIlC,IAAI;IAC7B;AACF;AAEA,MAAMmC,iBAAiB,CAACC;IACtB,MAAMC,QAAQC,gBAAgBF;IAC9B,OAAO;QACLrB,MAAM;QACNwB,QAAQH,MAAMI,OAAO,GAAG,AAAGJ,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO,GAAK,AAAC,KAAEJ,MAAMK,IAAI;QACzEJ;IACF;AACF;AAEA,MAAMK,iBAAiB,CAACC,OAAkB3B;IACxC,0IAA0I;IAC1I,kCAAkC;IAClC,MAAMqB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,yBAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,8BAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,mCAAmC;;IACtF,MAAMxB,QAA+B;QACnCV,MAAM;QACNmC,oBAAoBH,qBAAAA,aAAc;QAClCI,QAAQR;QACR3B,MAAMA,KAAKH,GAAG,CAAC,CAACqB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLnB,MAAM;gBACNf,MAAMvB,yBAAyByD;YACjC;QACF;IACF;IAEAhB,SAASM,eAAe,CAACC;AAC3B;AAEA,OAAO,MAAM2B,kBAAkB,CAACpC;IAC9B,MAAMqC,eAAerC,KAAKsC,MAAM,CAAC,CAACpB,MAAQA,eAAeU;IACzD,MAAMW,QAAQF,aAAaG,EAAE,CAAC;IAC9B,IAAID,OAAO;QACT,MAAME,SAAS/F,eAAe6F;QAC9B,IAAIE,QAAQ;YACVvC,SAASI,UAAU,GAAGmC;QACxB;IACF;IACA;;;;GAIC,GACD,MAAMpB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,yBAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,8BAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC;IAE7C,MAAMxB,QAAoC;QACxCV,MAAM;QACNoC,QAAQ;QACRO,mBAAmBX,qBAAAA,aAAc;QACjC/B,MAAMA,KAAKH,GAAG,CAAC,CAACqB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLnB,MAAM;gBACNf,MAAMvB,yBAAyByD;YACjC;QACF;IACF;IAEAhB,SAASM,eAAe,CAACC;AAC3B,EAAC;AAED,MAAMkC,2BAA2B,CAC/BC,WACAC,cACAC;IAEA,MAAMrC,QAA6B;QACjCV,MAAM;QACNwB,QAAQ,AAAC,cAAWqB,YAAU,OAAIC;QAClCxB,OAAOyB;QACPX,QAAQ;IACV;IAEAjC,SAASM,eAAe,CAACC;AAC3B;AAEA,MAAMa,kBAAkB,CAACF;IACvB,IAAI2B,aAAa;IACjBtG,yBAAyB2E;IACzB2B,aAAavG,cAAc4E,UAAU;IACrC,MAAMC,QAAQ,AAACD,CAAAA,MAAMC,KAAK,IAAI,EAAC,IAAK0B;IACpC,OAAO1B;AACT;AAEA,OAAO,SAAS2B,sBAAsBC,MAAe;IACnD,IAAIA,kBAAkBrB,OAAO;QAC3BsB,mCAAmCD,QAAQ3B,gBAAgB2B;QAC3D;IACF;IACAE,sCAAsCF;AACxC;AAEA,MAAMC,qCAAqC,CACzC9B,OACA0B;IAEA,MAAML,SAAS/F,eAAe0E;IAC9B,IAAIqB,QAAQ;QACVvC,SAASI,UAAU,GAAGmC;IACxB;IAEA,MAAMhC,QAAwB;QAC5BV,MAAM;QACNwB,QAAQ,AAAC,2BAAwBH,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO;QAC7DH,OAAOyB;QACPX,QAAQ;IACV;IAEAjC,SAASM,eAAe,CAACC;AAC3B;AAEA,MAAM0C,wCAAwC,CAACF;IAC7C,MAAMxC,QAAwB;QAC5BV,MAAM;QACN,kHAAkH;QAClH2C,mBAAmB;QACnBP,QAAQ;QACRnC,MAAM;YACJ;gBACED,MAAM;gBACNf,MAAO;gBACPoE,oBAAoB;YACtB;YACA;gBACErD,MAAM;gBACNf,MAAMvB,yBAAyBwF;YACjC;SACD;IACH;IAEA/C,SAASM,eAAe,CAACC;AAC3B;AAEA,MAAM4C,QAAQ,CAACrD;IACb,MAAMsD,WAAWtD,IAAI,CAAC,EAAE;IACxB,IAAI,OAAOsD,aAAa,UAAU;QAChC,OAAO;IACT;IACA,IAAIA,SAASC,UAAU,CAAC,mBAAmB;QACzC,OAAO;IACT;IAEA,IAAID,SAASC,UAAU,CAAC,UAAU;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAMC,eAAe,CAACxD;IACpB,IAAIA,KAAKyD,MAAM,GAAG,GAAG;QACnB,OAAO;IACT;IAEA,MAAM,CAACC,QAAQC,QAAQC,MAAM,GAAG5D;IAEhC,IACE,OAAO0D,WAAW,YAClB,OAAOC,WAAW,YAClB,OAAOC,UAAU,UACjB;QACA,OAAO;IACT;IAEA,0FAA0F;IAC1F,OAAOF,OAAOH,UAAU,CAAC,aAAaI,OAAOE,QAAQ,CAAC;AACxD;AAEA,OAAO,SAASC,sBAAsB1C,KAAY;IAChDuB,yBAAyBvB,MAAMK,IAAI,EAAEL,MAAMI,OAAO,EAAEF,gBAAgBF;AACtE;AAEA,sGAAsG;AACtG,OAAO,MAAM2C,+BAA+B,CAACxD;IAC3C,2BAA2B;IAC3B,IAAIb,WAAW;QACb;IACF;IACA,+GAA+G;IAC/G,IAAI,OAAOsE,WAAW,aAAa;QACjC;IACF;IAEA,+BAA+B;IAC/B,IAAI;QACFxG,QAAQyG,OAAO,CAAC,CAAC9B,SACftF,mBAAmBsF,QAAQ,SAAC+B;iDAAMlE;oBAAAA;;gBAChC,IAAIqD,MAAMrD,OAAO;oBACf;gBACF;gBACA,IAAIwD,aAAaxD,OAAO;oBACtB;gBACF;gBACA0B,eAAeS,QAAQnC;YACzB;IAEJ,EAAE,UAAM,CAAC;IACTE,SAASK,MAAM,GAAGA;IAClBb,YAAY;AACd,EAAC", "ignoreList": [0]}