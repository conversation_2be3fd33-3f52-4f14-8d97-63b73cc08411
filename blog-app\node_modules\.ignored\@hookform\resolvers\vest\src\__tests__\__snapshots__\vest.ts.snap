// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`vestResolver > should return all the error messages from vestResolver when validation fails and validateAllFieldCriteria set to true 1`] = `
{
  "errors": {
    "deepObject": {
      "data": {
        "message": "deepObject.data is required",
        "ref": undefined,
        "type": "",
        "types": {
          "0": "deepObject.data is required",
        },
      },
    },
    "password": {
      "message": "Password must be at least 5 chars",
      "ref": {
        "name": "password",
      },
      "type": "",
      "types": {
        "0": "Password must be at least 5 chars",
      },
    },
    "username": {
      "message": "Username is required",
      "ref": {
        "name": "username",
      },
      "type": "",
      "types": {
        "0": "Username is required",
      },
    },
  },
  "values": {},
}
`;

exports[`vestResolver > should return all the error messages from vestResolver when validation fails and validateAllFieldCriteria set to true and \`mode: sync\` 1`] = `
{
  "errors": {
    "deepObject": {
      "data": {
        "message": "deepObject.data is required",
        "ref": undefined,
        "type": "",
        "types": {
          "0": "deepObject.data is required",
        },
      },
    },
    "password": {
      "message": "Password must be at least 5 chars",
      "ref": {
        "name": "password",
      },
      "type": "",
      "types": {
        "0": "Password must be at least 5 chars",
      },
    },
    "username": {
      "message": "Username is required",
      "ref": {
        "name": "username",
      },
      "type": "",
      "types": {
        "0": "Username is required",
      },
    },
  },
  "values": {},
}
`;

exports[`vestResolver > should return single error message from vestResolver when validation fails and validateAllFieldCriteria set to false 1`] = `
{
  "errors": {
    "deepObject": {
      "data": {
        "message": "deepObject.data is required",
        "ref": undefined,
        "type": "",
      },
    },
    "password": {
      "message": "Password must be at least 5 chars",
      "ref": {
        "name": "password",
      },
      "type": "",
    },
    "username": {
      "message": "Username is required",
      "ref": {
        "name": "username",
      },
      "type": "",
    },
  },
  "values": {},
}
`;

exports[`vestResolver > should return single error message from vestResolver when validation fails and validateAllFieldCriteria set to false and \`mode: sync\` 1`] = `
{
  "errors": {
    "deepObject": {
      "data": {
        "message": "deepObject.data is required",
        "ref": undefined,
        "type": "",
      },
    },
    "password": {
      "message": "Password must be at least 5 chars",
      "ref": {
        "name": "password",
      },
      "type": "",
    },
    "username": {
      "message": "Username is required",
      "ref": {
        "name": "username",
      },
      "type": "",
    },
  },
  "values": {},
}
`;
