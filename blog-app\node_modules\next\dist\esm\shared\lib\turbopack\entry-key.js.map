{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/entry-key.ts"], "sourcesContent": ["/**\n * `app` -> app dir\n * `pages` -> pages dir\n * `root` -> middleware / instrumentation\n * `assets` -> assets\n */\nexport type EntryKeyType = 'app' | 'pages' | 'root' | 'assets'\nexport type EntryKeySide = 'client' | 'server'\n\n// custom type to make sure you can't accidentally use a \"generic\" string\nexport type EntryKey =\n  `{\"type\":\"${EntryKeyType}\",\"side\":\"${EntryKeyType}\",\"page\":\"${string}\"}`\n\n/**\n * Get a key that's unique across all entrypoints.\n */\nexport function getEntryKey(\n  type: EntryKeyType,\n  side: EntryKeySide,\n  page: string\n): EntryKey {\n  return JSON.stringify({ type, side, page }) as EntryKey\n}\n\n/**\n * Split an `EntryKey` up into its components.\n */\nexport function splitEntryKey(key: EntryKey): {\n  type: EntryKeyType\n  side: EntryKeySide\n  page: string\n} {\n  return JSON.parse(key)\n}\n"], "names": ["getEntry<PERSON>ey", "type", "side", "page", "JSON", "stringify", "splitEntryKey", "key", "parse"], "mappings": "AAAA;;;;;CAKC,GAQD;;CAEC,GACD,OAAO,SAASA,YACdC,IAAkB,EAClBC,IAAkB,EAClBC,IAAY;IAEZ,OAAOC,KAAKC,SAAS,CAAC;QAAEJ;QAAMC;QAAMC;IAAK;AAC3C;AAEA;;CAEC,GACD,OAAO,SAASG,cAAcC,GAAa;IAKzC,OAAOH,KAAKI,KAAK,CAACD;AACpB", "ignoreList": [0]}