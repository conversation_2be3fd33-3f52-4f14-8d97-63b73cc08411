{"version": 3, "sources": ["../../../src/server/app-render/after-task-async-storage-instance.ts"], "sourcesContent": ["import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["createAsyncLocalStorage", "afterTaskAsyncStorageInstance"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,wBAAuB;AAE/D,OAAO,MAAMC,gCACXD,0BAAyB", "ignoreList": [0]}