{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-prefetch-segment-data-route.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { getNamedRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport {\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n} from '../../../lib/constants'\n\nexport const SEGMENT_PATH_KEY = 'nextSegmentPath'\n\nexport type PrefetchSegmentDataRoute = {\n  source: string\n  destination: string\n  routeKeys: { [key: string]: string }\n}\n\nexport function buildPrefetchSegmentDataRoute(\n  page: string,\n  segmentPath: string\n): PrefetchSegmentDataRoute {\n  const pagePath = normalizePagePath(page)\n\n  const destination = path.posix.join(\n    `${pagePath}${RSC_SEGMENTS_DIR_SUFFIX}`,\n    `${segmentPath}${RSC_SEGMENT_SUFFIX}`\n  )\n\n  const { namedRegex, routeKeys } = getNamedRouteRegex(destination, {\n    prefixRouteKeys: true,\n    includePrefix: true,\n    includeSuffix: true,\n    excludeOptionalTrailingSlash: true,\n    backreferenceDuplicateKeys: true,\n  })\n\n  return {\n    destination,\n    source: namedRegex,\n    routeKeys,\n  }\n}\n\n/**\n * Builds a prefetch segment data route that is inverted. This means that it's\n * supposed to rewrite from the previous segment paths route back to the\n * prefetch RSC route.\n *\n * @param page - The page to build the route for.\n * @param segmentPath - The segment path to build the route for.\n * @returns The prefetch segment data route.\n */\nexport function buildInversePrefetchSegmentDataRoute(\n  page: string,\n  segmentPath: string\n): PrefetchSegmentDataRoute {\n  const pagePath = normalizePagePath(page)\n\n  const source = path.posix.join(\n    `${pagePath}${RSC_SEGMENTS_DIR_SUFFIX}`,\n    `${segmentPath}${RSC_SEGMENT_SUFFIX}`\n  )\n\n  const { namedRegex, routeKeys } = getNamedRouteRegex(source, {\n    prefixRouteKeys: true,\n    includePrefix: true,\n    includeSuffix: true,\n    excludeOptionalTrailingSlash: true,\n    backreferenceDuplicateKeys: true,\n  })\n\n  const destination = path.posix.join(`${pagePath}${RSC_PREFETCH_SUFFIX}`)\n\n  return {\n    source: namedRegex,\n    destination,\n    routeKeys,\n  }\n}\n"], "names": ["path", "normalizePagePath", "getNamedRouteRegex", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "SEGMENT_PATH_KEY", "buildPrefetchSegmentDataRoute", "page", "segmentPath", "pagePath", "destination", "posix", "join", "namedRegex", "routeKeys", "prefixRouteKeys", "includePrefix", "includeSuffix", "excludeOptionalTrailingSlash", "backreferenceDuplicateKeys", "source", "buildInversePrefetchSegmentDataRoute"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AACtD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,uBAAuB,QAClB,yBAAwB;AAE/B,OAAO,MAAMC,mBAAmB,kBAAiB;AAQjD,OAAO,SAASC,8BACdC,IAAY,EACZC,WAAmB;IAEnB,MAAMC,WAAWT,kBAAkBO;IAEnC,MAAMG,cAAcX,KAAKY,KAAK,CAACC,IAAI,CACjC,GAAGH,WAAWL,yBAAyB,EACvC,GAAGI,cAAcL,oBAAoB;IAGvC,MAAM,EAAEU,UAAU,EAAEC,SAAS,EAAE,GAAGb,mBAAmBS,aAAa;QAChEK,iBAAiB;QACjBC,eAAe;QACfC,eAAe;QACfC,8BAA8B;QAC9BC,4BAA4B;IAC9B;IAEA,OAAO;QACLT;QACAU,QAAQP;QACRC;IACF;AACF;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASO,qCACdd,IAAY,EACZC,WAAmB;IAEnB,MAAMC,WAAWT,kBAAkBO;IAEnC,MAAMa,SAASrB,KAAKY,KAAK,CAACC,IAAI,CAC5B,GAAGH,WAAWL,yBAAyB,EACvC,GAAGI,cAAcL,oBAAoB;IAGvC,MAAM,EAAEU,UAAU,EAAEC,SAAS,EAAE,GAAGb,mBAAmBmB,QAAQ;QAC3DL,iBAAiB;QACjBC,eAAe;QACfC,eAAe;QACfC,8BAA8B;QAC9BC,4BAA4B;IAC9B;IAEA,MAAMT,cAAcX,KAAKY,KAAK,CAACC,IAAI,CAAC,GAAGH,WAAWP,qBAAqB;IAEvE,OAAO;QACLkB,QAAQP;QACRH;QACAI;IACF;AACF", "ignoreList": [0]}