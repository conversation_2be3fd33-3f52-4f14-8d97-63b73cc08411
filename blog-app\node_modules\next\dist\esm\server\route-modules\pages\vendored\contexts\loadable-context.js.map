{"version": 3, "sources": ["../../../../../../src/server/route-modules/pages/vendored/contexts/loadable-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].LoadableContext\n"], "names": ["module", "exports", "require", "vendored", "LoadableContext"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,eAAe", "ignoreList": [0]}