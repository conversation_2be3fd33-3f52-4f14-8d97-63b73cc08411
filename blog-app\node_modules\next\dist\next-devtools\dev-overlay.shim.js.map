{"version": 3, "sources": ["../../src/next-devtools/dev-overlay.shim.ts"], "sourcesContent": ["export function renderAppDevOverlay() {\n  throw new Error(\n    \"Next DevTools: Can't render in this environment. This is a bug in Next.js\"\n  )\n}\n\nexport function renderPagesDevOverlay() {\n  throw new Error(\n    \"Next DevTools: Can't render in this environment. This is a bug in Next.js\"\n  )\n}\n\n// TODO: Extract into separate functions that are imported\nexport const dispatcher = new Proxy(\n  {},\n  {\n    get: (_, prop) => {\n      return () => {\n        throw new Error(\n          `Next DevTools: Can't dispatch ${String(prop)} in this environment. This is a bug in Next.js`\n        )\n      }\n    },\n  }\n)\n"], "names": ["dispatcher", "renderAppDevOverlay", "renderPagesDevOverlay", "Error", "Proxy", "get", "_", "prop", "String"], "mappings": ";;;;;;;;;;;;;;;;IAaaA,UAAU;eAAVA;;IAbGC,mBAAmB;eAAnBA;;IAMAC,qBAAqB;eAArBA;;;AANT,SAASD;IACd,MAAM,qBAEL,CAFK,IAAIE,MACR,8EADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASD;IACd,MAAM,qBAEL,CAFK,IAAIC,MACR,8EADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAGO,MAAMH,aAAa,IAAII,MAC5B,CAAC,GACD;IACEC,KAAK,CAACC,GAAGC;QACP,OAAO;YACL,MAAM,qBAEL,CAFK,IAAIJ,MACR,AAAC,mCAAgCK,OAAOD,QAAM,mDAD1C,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0]}