{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/index.ts"], "sourcesContent": ["export { originConsoleError } from './intercept-console-error'\nexport { handleClientError } from './use-error-handler'\nexport { decorateDevError } from './stitched-error'\n"], "names": ["originConsoleError", "handleClientError", "decorateDevError"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,4BAA2B;AAC9D,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,gBAAgB,QAAQ,mBAAkB", "ignoreList": [0]}