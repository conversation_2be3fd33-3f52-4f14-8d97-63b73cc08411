{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/get-asset-path-from-route.ts"], "sourcesContent": ["// Translates a logical route into its pages asset path (relative from a common prefix)\n// \"asset path\" being its javascript file, data file, prerendered html,...\nexport default function getAssetPathFromRoute(\n  route: string,\n  ext: string = ''\n): string {\n  const path =\n    route === '/'\n      ? '/index'\n      : /^\\/index(\\/|$)/.test(route)\n        ? `/index${route}`\n        : route\n  return path + ext\n}\n"], "names": ["getAssetPathFromRoute", "route", "ext", "path", "test"], "mappings": "AAAA,uFAAuF;AACvF,0EAA0E;AAC1E,eAAe,SAASA,sBACtBC,KAAa,EACbC,GAAgB;IAAhBA,IAAAA,gBAAAA,MAAc;IAEd,MAAMC,OACJF,UAAU,MACN,WACA,iBAAiBG,IAAI,CAACH,SACpB,AAAC,WAAQA,QACTA;IACR,OAAOE,OAAOD;AAChB", "ignoreList": [0]}