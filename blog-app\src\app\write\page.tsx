'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Save, Eye, Send, Image as ImageIcon, Bold, Italic, Link as LinkIcon, List, Code } from 'lucide-react'
import { Header } from '@/components/layout/Header'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useAuth } from '@/contexts/AuthContext'

export default function WritePage() {
  const router = useRouter()
  const { user } = useAuth()
  
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    category: '',
    tags: '',
    published: false
  })
  
  const [isPreview, setIsPreview] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // 如果用户未登录，重定向到登录页
  React.useEffect(() => {
    if (!user) {
      router.push('/auth/login')
    }
  }, [user, router])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSave = async (publish = false) => {
    setIsSaving(true)
    try {
      // 这里将来会连接到 Supabase
      console.log('保存文章:', { ...formData, published: publish })
      
      // 模拟保存延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (publish) {
        router.push('/')
      }
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const insertMarkdown = (syntax: string, placeholder = '') => {
    const textarea = document.querySelector('textarea[name="content"]') as HTMLTextAreaElement
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = textarea.value.substring(start, end)
    const replacement = selectedText || placeholder

    let newText = ''
    switch (syntax) {
      case 'bold':
        newText = `**${replacement}**`
        break
      case 'italic':
        newText = `*${replacement}*`
        break
      case 'link':
        newText = `[${replacement || '链接文本'}](url)`
        break
      case 'image':
        newText = `![${replacement || '图片描述'}](图片链接)`
        break
      case 'code':
        newText = `\`${replacement}\``
        break
      case 'list':
        newText = `- ${replacement || '列表项'}`
        break
      default:
        newText = replacement
    }

    const newValue = textarea.value.substring(0, start) + newText + textarea.value.substring(end)
    setFormData(prev => ({ ...prev, content: newValue }))

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + newText.length, start + newText.length)
    }, 0)
  }

  if (!user) {
    return null // 或者显示加载状态
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              写文章
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              分享您的想法和见解
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 编辑器 */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>
                      {isPreview ? '预览' : '编辑'}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsPreview(!isPreview)}
                      >
                        <Eye size={16} className="mr-1" />
                        {isPreview ? '编辑' : '预览'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <Input
                    name="title"
                    placeholder="文章标题"
                    value={formData.title}
                    onChange={handleChange}
                    className="text-2xl font-bold border-none px-0 focus:ring-0"
                  />

                  {!isPreview && (
                    <>
                      {/* Markdown 工具栏 */}
                      <div className="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('bold', '粗体文本')}
                          className="p-2"
                        >
                          <Bold size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('italic', '斜体文本')}
                          className="p-2"
                        >
                          <Italic size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('link')}
                          className="p-2"
                        >
                          <LinkIcon size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('image')}
                          className="p-2"
                        >
                          <ImageIcon size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('code', '代码')}
                          className="p-2"
                        >
                          <Code size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => insertMarkdown('list')}
                          className="p-2"
                        >
                          <List size={16} />
                        </Button>
                      </div>

                      <textarea
                        name="content"
                        placeholder="开始写作..."
                        value={formData.content}
                        onChange={handleChange}
                        className="w-full h-96 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </>
                  )}

                  {isPreview && (
                    <div className="prose prose-lg dark:prose-invert max-w-none">
                      <h1>{formData.title || '文章标题'}</h1>
                      <div className="whitespace-pre-wrap">
                        {formData.content || '文章内容将在这里显示...'}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 发布设置 */}
              <Card>
                <CardHeader>
                  <CardTitle>发布设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      分类
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                    >
                      <option value="">选择分类</option>
                      <option value="技术">技术</option>
                      <option value="设计">设计</option>
                      <option value="生活">生活</option>
                      <option value="随笔">随笔</option>
                    </select>
                  </div>

                  <Input
                    name="tags"
                    label="标签"
                    placeholder="用逗号分隔多个标签"
                    value={formData.tags}
                    onChange={handleChange}
                  />

                  <Input
                    name="excerpt"
                    label="摘要"
                    placeholder="文章简介（可选）"
                    value={formData.excerpt}
                    onChange={handleChange}
                  />

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      name="published"
                      checked={formData.published}
                      onChange={handleChange}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label className="text-sm text-gray-700 dark:text-gray-300">
                      立即发布
                    </label>
                  </div>
                </CardContent>
              </Card>

              {/* 操作按钮 */}
              <div className="space-y-3">
                <Button
                  onClick={() => handleSave(true)}
                  loading={isSaving}
                  disabled={!formData.title || !formData.content}
                  className="w-full"
                >
                  <Send size={16} className="mr-2" />
                  {formData.published ? '发布文章' : '保存并发布'}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => handleSave(false)}
                  loading={isSaving}
                  className="w-full"
                >
                  <Save size={16} className="mr-2" />
                  保存草稿
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
