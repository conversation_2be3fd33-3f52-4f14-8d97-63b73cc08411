{"version": 3, "sources": ["../../src/server/load-manifest.external.ts"], "sourcesContent": ["import type { DeepReadonly } from '../shared/lib/deep-readonly'\n\nimport { join } from 'path'\nimport { readFileSync } from 'fs'\nimport { runInNewContext } from 'vm'\nimport { deepFreeze } from '../shared/lib/deep-freeze'\n\nconst sharedCache = new Map<string, unknown>()\n\n/**\n * Load a manifest file from the file system. Optionally cache the manifest in\n * memory to avoid reading the file multiple times using the provided cache or\n * defaulting to a shared module cache. The manifest is frozen to prevent\n * modifications if it is cached.\n *\n * @param path the path to the manifest file\n * @param shouldCache whether to cache the manifest in memory\n * @param cache the cache to use for storing the manifest\n * @returns the manifest object\n */\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>,\n  skipParse?: boolean\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>,\n  skipParse?: boolean\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache,\n  skipParse = false\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  let manifest: any = readFileSync(/* turbopackIgnore: true */ path, 'utf8')\n\n  if (!skipParse) {\n    manifest = JSON.parse(manifest)\n\n    // Freeze the manifest so it cannot be modified if we're caching it.\n    if (shouldCache) {\n      manifest = deepFreeze(manifest)\n    }\n  }\n\n  if (shouldCache) {\n    cache.set(path, manifest)\n  }\n\n  return manifest\n}\n\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  const content = readFileSync(/* turbopackIgnore: true */ path, 'utf8')\n  if (content.length === 0) {\n    throw new Error('Manifest file is empty')\n  }\n\n  let contextObject = {}\n  runInNewContext(content, contextObject)\n\n  // Freeze the context object so it cannot be modified if we're caching it.\n  if (shouldCache) {\n    contextObject = deepFreeze(contextObject)\n  }\n\n  if (shouldCache) {\n    cache.set(path, contextObject)\n  }\n\n  return contextObject as T\n}\n\nexport function loadManifestFromRelativePath<T extends object>({\n  projectDir,\n  distDir,\n  manifest,\n  shouldCache,\n  cache,\n  skipParse,\n  handleMissing,\n  useEval,\n}: {\n  projectDir: string\n  distDir: string\n  manifest: string\n  shouldCache?: boolean\n  cache?: Map<string, unknown>\n  skipParse?: boolean\n  handleMissing?: boolean\n  useEval?: boolean\n}): DeepReadonly<T> {\n  try {\n    const manifestPath = join(\n      /* turbopackIgnore: true */ projectDir,\n      distDir,\n      manifest\n    )\n\n    if (useEval) {\n      return evalManifest<T>(manifestPath, shouldCache, cache)\n    }\n    return loadManifest<T>(manifestPath, shouldCache, cache, skipParse)\n  } catch (err) {\n    if (handleMissing) {\n      // TODO: should this be undefined\n      return {} as DeepReadonly<T>\n    }\n    throw err\n  }\n}\n\nexport function clearManifestCache(path: string, cache = sharedCache): boolean {\n  return cache.delete(path)\n}\n"], "names": ["join", "readFileSync", "runInNewContext", "deepFreeze", "sharedCache", "Map", "loadManifest", "path", "shouldCache", "cache", "<PERSON><PERSON><PERSON><PERSON>", "cached", "get", "manifest", "JSON", "parse", "set", "evalManifest", "content", "length", "Error", "contextObject", "loadManifestFromRelativePath", "projectDir", "distDir", "handleMissing", "useEval", "manifestPath", "err", "clearManifestCache", "delete"], "mappings": "AAEA,SAASA,IAAI,QAAQ,OAAM;AAC3B,SAASC,YAAY,QAAQ,KAAI;AACjC,SAASC,eAAe,QAAQ,KAAI;AACpC,SAASC,UAAU,QAAQ,4BAA2B;AAEtD,MAAMC,cAAc,IAAIC;AA6BxB,OAAO,SAASC,aACdC,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQL,WAAW,EACnBM,YAAY,KAAK;IAEjB,MAAMC,SAASH,eAAeC,MAAMG,GAAG,CAACL;IACxC,IAAII,QAAQ;QACV,OAAOA;IACT;IAEA,IAAIE,WAAgBZ,aAAa,yBAAyB,GAAGM,MAAM;IAEnE,IAAI,CAACG,WAAW;QACdG,WAAWC,KAAKC,KAAK,CAACF;QAEtB,oEAAoE;QACpE,IAAIL,aAAa;YACfK,WAAWV,WAAWU;QACxB;IACF;IAEA,IAAIL,aAAa;QACfC,MAAMO,GAAG,CAACT,MAAMM;IAClB;IAEA,OAAOA;AACT;AAgBA,OAAO,SAASI,aACdV,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQL,WAAW;IAEnB,MAAMO,SAASH,eAAeC,MAAMG,GAAG,CAACL;IACxC,IAAII,QAAQ;QACV,OAAOA;IACT;IAEA,MAAMO,UAAUjB,aAAa,yBAAyB,GAAGM,MAAM;IAC/D,IAAIW,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAIC,gBAAgB,CAAC;IACrBnB,gBAAgBgB,SAASG;IAEzB,0EAA0E;IAC1E,IAAIb,aAAa;QACfa,gBAAgBlB,WAAWkB;IAC7B;IAEA,IAAIb,aAAa;QACfC,MAAMO,GAAG,CAACT,MAAMc;IAClB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASC,6BAA+C,EAC7DC,UAAU,EACVC,OAAO,EACPX,QAAQ,EACRL,WAAW,EACXC,KAAK,EACLC,SAAS,EACTe,aAAa,EACbC,OAAO,EAUR;IACC,IAAI;QACF,MAAMC,eAAe3B,KACnB,yBAAyB,GAAGuB,YAC5BC,SACAX;QAGF,IAAIa,SAAS;YACX,OAAOT,aAAgBU,cAAcnB,aAAaC;QACpD;QACA,OAAOH,aAAgBqB,cAAcnB,aAAaC,OAAOC;IAC3D,EAAE,OAAOkB,KAAK;QACZ,IAAIH,eAAe;YACjB,iCAAiC;YACjC,OAAO,CAAC;QACV;QACA,MAAMG;IACR;AACF;AAEA,OAAO,SAASC,mBAAmBtB,IAAY,EAAEE,QAAQL,WAAW;IAClE,OAAOK,MAAMqB,MAAM,CAACvB;AACtB", "ignoreList": [0]}