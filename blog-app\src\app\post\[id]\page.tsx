'use client'

import React, { useState } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { Calendar, Clock, Heart, MessageCircle, Share2, Bookmark, ArrowLeft, User } from 'lucide-react'
import { Header } from '@/components/layout/Header'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'

// 模拟文章数据
const mockPost = {
  id: '1',
  title: 'React 18 新特性深度解析',
  content: `# React 18 新特性深度解析

React 18 是 React 的一个重要版本，引入了许多令人兴奋的新特性和改进。在这篇文章中，我们将深入探讨这些新特性，并了解如何在实际项目中应用它们。

## 并发特性 (Concurrent Features)

React 18 最重要的更新之一就是并发特性的引入。这些特性允许 React 在渲染过程中暂停、恢复或放弃工作，从而提供更好的用户体验。

### Automatic Batching

在 React 18 之前，React 只会在事件处理器中批量更新状态。现在，React 会自动批量处理所有状态更新，包括在 Promise、setTimeout 和原生事件处理器中的更新。

\`\`\`javascript
// React 18 之前，这些更新不会被批量处理
setTimeout(() => {
  setCount(c => c + 1);
  setFlag(f => !f);
  // React 会渲染两次，每次状态更新一次
}, 1000);

// React 18 中，这些更新会被自动批量处理
setTimeout(() => {
  setCount(c => c + 1);
  setFlag(f => !f);
  // React 只会渲染一次
}, 1000);
\`\`\`

### Suspense 改进

React 18 对 Suspense 进行了重大改进，现在支持服务端渲染，并且提供了更好的错误边界处理。

\`\`\`jsx
function App() {
  return (
    <Suspense fallback={<Loading />}>
      <ComponentThatSuspendsOnData />
      <Sibling />
    </Suspense>
  );
}
\`\`\`

## 新的 Hooks

React 18 还引入了几个新的 Hooks，让开发者能够更好地利用并发特性。

### useId

\`useId\` Hook 用于生成唯一的 ID，特别适用于可访问性属性。

\`\`\`javascript
function Checkbox() {
  const id = useId();
  return (
    <>
      <label htmlFor={id}>选择我</label>
      <input id={id} type="checkbox" name="checkbox"/>
    </>
  );
}
\`\`\`

### useTransition

\`useTransition\` 允许你将状态更新标记为非紧急的，让 React 知道可以中断这些更新来处理更重要的任务。

\`\`\`javascript
function App() {
  const [isPending, startTransition] = useTransition();
  const [count, setCount] = useState(0);
  
  function handleClick() {
    startTransition(() => {
      setCount(c => c + 1);
    });
  }

  return (
    <div>
      {isPending && <Spinner />}
      <button onClick={handleClick}>{count}</button>
    </div>
  );
}
\`\`\`

## 总结

React 18 带来的这些新特性为构建更好的用户体验提供了强大的工具。并发特性让应用更加响应，新的 Hooks 提供了更多的控制能力，而 Suspense 的改进则让数据获取变得更加优雅。

在升级到 React 18 时，大多数应用都能够平滑过渡，因为这些新特性都是可选的。但是，了解和掌握这些特性将帮助你构建更好的 React 应用。`,
  excerpt: '探索 React 18 带来的并发特性、自动批处理、Suspense 改进等重要更新，以及如何在项目中应用这些新特性。',
  author: {
    id: '1',
    name: '张三',
    avatar: '/avatars/avatar1.jpg',
    bio: '前端开发工程师，专注于 React 生态系统'
  },
  category: '技术',
  tags: ['React', 'JavaScript', '前端开发'],
  publishedAt: '2024-01-15',
  updatedAt: '2024-01-15',
  readTime: 8,
  likes: 42,
  comments: 12,
  isLiked: false,
  isBookmarked: false
}

export default function PostPage() {
  const params = useParams()
  const [post, setPost] = useState(mockPost)
  const [isLiked, setIsLiked] = useState(post.isLiked)
  const [isBookmarked, setIsBookmarked] = useState(post.isBookmarked)
  const [likesCount, setLikesCount] = useState(post.likes)

  const handleLike = () => {
    setIsLiked(!isLiked)
    setLikesCount(prev => isLiked ? prev - 1 : prev + 1)
  }

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        })
      } catch (error) {
        console.log('分享失败:', error)
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      alert('链接已复制到剪贴板')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* 返回按钮 */}
          <Button
            variant="ghost"
            onClick={() => window.history.back()}
            className="mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            返回
          </Button>

          {/* 文章头部 */}
          <div className="mb-8">
            <div className="flex items-center space-x-2 mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {post.category}
              </span>
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                >
                  #{tag}
                </span>
              ))}
            </div>

            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {post.title}
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
              {post.excerpt}
            </p>

            {/* 作者信息和元数据 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {post.author.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {post.author.bio}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center">
                  <Calendar size={16} className="mr-1" />
                  {post.publishedAt}
                </div>
                <div className="flex items-center">
                  <Clock size={16} className="mr-1" />
                  {post.readTime} 分钟阅读
                </div>
              </div>
            </div>
          </div>

          {/* 文章内容 */}
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="prose prose-lg dark:prose-invert max-w-none">
                <div className="whitespace-pre-wrap">
                  {post.content}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 互动按钮 */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                variant={isLiked ? 'primary' : 'outline'}
                onClick={handleLike}
                className="flex items-center space-x-2"
              >
                <Heart size={16} className={isLiked ? 'fill-current' : ''} />
                <span>{likesCount}</span>
              </Button>

              <Button
                variant="outline"
                className="flex items-center space-x-2"
              >
                <MessageCircle size={16} />
                <span>{post.comments}</span>
              </Button>

              <Button
                variant={isBookmarked ? 'primary' : 'outline'}
                onClick={handleBookmark}
              >
                <Bookmark size={16} className={isBookmarked ? 'fill-current' : ''} />
              </Button>

              <Button
                variant="outline"
                onClick={handleShare}
              >
                <Share2 size={16} />
              </Button>
            </div>
          </div>

          {/* 作者卡片 */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {post.author.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {post.author.bio}
                  </p>
                </div>
                <Button variant="outline">
                  关注
                </Button>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
