{"version": 3, "sources": ["../../../../src/lib/metadata/generate/meta.tsx"], "sourcesContent": ["import React from 'react'\nimport { nonNullable } from '../../non-nullable'\n\nexport function Meta({\n  name,\n  property,\n  content,\n  media,\n}: {\n  name?: string\n  property?: string\n  media?: string\n  content: string | number | URL | null | undefined\n}): React.ReactElement | null {\n  if (typeof content !== 'undefined' && content !== null && content !== '') {\n    return (\n      <meta\n        {...(name ? { name } : { property })}\n        {...(media ? { media } : undefined)}\n        content={typeof content === 'string' ? content : content.toString()}\n      />\n    )\n  }\n  return null\n}\n\nexport function MetaFilter<T extends {} | {}[]>(\n  items: (T | null)[]\n): NonNullable<T>[] {\n  const acc: NonNullable<T>[] = []\n  for (const item of items) {\n    if (Array.isArray(item)) {\n      acc.push(...item.filter(nonNullable))\n    } else if (nonNullable(item)) {\n      acc.push(item)\n    }\n  }\n  return acc\n}\n\ntype ExtendMetaContent = Record<\n  string,\n  undefined | string | URL | number | boolean | null | undefined\n>\ntype MultiMetaContent =\n  | (ExtendMetaContent | string | URL | number)[]\n  | null\n  | undefined\n\nfunction camelToSnake(camelCaseStr: string) {\n  return camelCaseStr.replace(/([A-Z])/g, function (match) {\n    return '_' + match.toLowerCase()\n  })\n}\n\nconst aliasPropPrefixes = new Set([\n  'og:image',\n  'twitter:image',\n  'og:video',\n  'og:audio',\n])\nfunction getMetaKey(prefix: string, key: string) {\n  // Use `twitter:image` and `og:image` instead of `twitter:image:url` and `og:image:url`\n  // to be more compatible as it's a more common format.\n  // `og:video` & `og:audio` do not have a `:url` suffix alias\n  if (aliasPropPrefixes.has(prefix) && key === 'url') {\n    return prefix\n  }\n  if (prefix.startsWith('og:') || prefix.startsWith('twitter:')) {\n    key = camelToSnake(key)\n  }\n  return prefix + ':' + key\n}\n\nfunction ExtendMeta({\n  content,\n  namePrefix,\n  propertyPrefix,\n}: {\n  content?: ExtendMetaContent\n  namePrefix?: string\n  propertyPrefix?: string\n}) {\n  if (!content) return null\n  return MetaFilter(\n    Object.entries(content).map(([k, v]) => {\n      return typeof v === 'undefined'\n        ? null\n        : Meta({\n            ...(propertyPrefix && { property: getMetaKey(propertyPrefix, k) }),\n            ...(namePrefix && { name: getMetaKey(namePrefix, k) }),\n            content: typeof v === 'string' ? v : v?.toString(),\n          })\n    })\n  )\n}\n\nexport function MultiMeta({\n  propertyPrefix,\n  namePrefix,\n  contents,\n}: {\n  propertyPrefix?: string\n  namePrefix?: string\n  contents?: MultiMetaContent | null\n}) {\n  if (typeof contents === 'undefined' || contents === null) {\n    return null\n  }\n\n  return MetaFilter(\n    contents.map((content) => {\n      if (\n        typeof content === 'string' ||\n        typeof content === 'number' ||\n        content instanceof URL\n      ) {\n        return Meta({\n          ...(propertyPrefix\n            ? { property: propertyPrefix }\n            : { name: namePrefix }),\n          content,\n        })\n      } else {\n        return ExtendMeta({\n          namePrefix,\n          propertyPrefix,\n          content,\n        })\n      }\n    })\n  )\n}\n"], "names": ["React", "nonNullable", "Meta", "name", "property", "content", "media", "meta", "undefined", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "items", "acc", "item", "Array", "isArray", "push", "filter", "camelToSnake", "camelCaseStr", "replace", "match", "toLowerCase", "aliasPropPrefixes", "Set", "getMetaKey", "prefix", "key", "has", "startsWith", "ExtendMeta", "namePrefix", "propertyPrefix", "Object", "entries", "map", "k", "v", "MultiMeta", "contents", "URL"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,WAAW,QAAQ,qBAAoB;AAEhD,OAAO,SAASC,KAAK,EACnBC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,KAAK,EAMN;IACC,IAAI,OAAOD,YAAY,eAAeA,YAAY,QAAQA,YAAY,IAAI;QACxE,qBACE,KAACE;YACE,GAAIJ,OAAO;gBAAEA;YAAK,IAAI;gBAAEC;YAAS,CAAC;YAClC,GAAIE,QAAQ;gBAAEA;YAAM,IAAIE,SAAS;YAClCH,SAAS,OAAOA,YAAY,WAAWA,UAAUA,QAAQI,QAAQ;;IAGvE;IACA,OAAO;AACT;AAEA,OAAO,SAASC,WACdC,KAAmB;IAEnB,MAAMC,MAAwB,EAAE;IAChC,KAAK,MAAMC,QAAQF,MAAO;QACxB,IAAIG,MAAMC,OAAO,CAACF,OAAO;YACvBD,IAAII,IAAI,IAAIH,KAAKI,MAAM,CAAChB;QAC1B,OAAO,IAAIA,YAAYY,OAAO;YAC5BD,IAAII,IAAI,CAACH;QACX;IACF;IACA,OAAOD;AACT;AAWA,SAASM,aAAaC,YAAoB;IACxC,OAAOA,aAAaC,OAAO,CAAC,YAAY,SAAUC,KAAK;QACrD,OAAO,MAAMA,MAAMC,WAAW;IAChC;AACF;AAEA,MAAMC,oBAAoB,IAAIC,IAAI;IAChC;IACA;IACA;IACA;CACD;AACD,SAASC,WAAWC,MAAc,EAAEC,GAAW;IAC7C,uFAAuF;IACvF,sDAAsD;IACtD,4DAA4D;IAC5D,IAAIJ,kBAAkBK,GAAG,CAACF,WAAWC,QAAQ,OAAO;QAClD,OAAOD;IACT;IACA,IAAIA,OAAOG,UAAU,CAAC,UAAUH,OAAOG,UAAU,CAAC,aAAa;QAC7DF,MAAMT,aAAaS;IACrB;IACA,OAAOD,SAAS,MAAMC;AACxB;AAEA,SAASG,WAAW,EAClBzB,OAAO,EACP0B,UAAU,EACVC,cAAc,EAKf;IACC,IAAI,CAAC3B,SAAS,OAAO;IACrB,OAAOK,WACLuB,OAAOC,OAAO,CAAC7B,SAAS8B,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE;QACjC,OAAO,OAAOA,MAAM,cAChB,OACAnC,KAAK;YACH,GAAI8B,kBAAkB;gBAAE5B,UAAUqB,WAAWO,gBAAgBI;YAAG,CAAC;YACjE,GAAIL,cAAc;gBAAE5B,MAAMsB,WAAWM,YAAYK;YAAG,CAAC;YACrD/B,SAAS,OAAOgC,MAAM,WAAWA,IAAIA,qBAAAA,EAAG5B,QAAQ;QAClD;IACN;AAEJ;AAEA,OAAO,SAAS6B,UAAU,EACxBN,cAAc,EACdD,UAAU,EACVQ,QAAQ,EAKT;IACC,IAAI,OAAOA,aAAa,eAAeA,aAAa,MAAM;QACxD,OAAO;IACT;IAEA,OAAO7B,WACL6B,SAASJ,GAAG,CAAC,CAAC9B;QACZ,IACE,OAAOA,YAAY,YACnB,OAAOA,YAAY,YACnBA,mBAAmBmC,KACnB;YACA,OAAOtC,KAAK;gBACV,GAAI8B,iBACA;oBAAE5B,UAAU4B;gBAAe,IAC3B;oBAAE7B,MAAM4B;gBAAW,CAAC;gBACxB1B;YACF;QACF,OAAO;YACL,OAAOyB,WAAW;gBAChBC;gBACAC;gBACA3B;YACF;QACF;IACF;AAEJ", "ignoreList": [0]}