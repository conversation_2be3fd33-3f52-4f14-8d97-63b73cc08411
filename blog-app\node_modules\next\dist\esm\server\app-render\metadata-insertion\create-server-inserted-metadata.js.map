{"version": 3, "sources": ["../../../../src/server/app-render/metadata-insertion/create-server-inserted-metadata.tsx"], "sourcesContent": ["/**\n * For chromium based browsers (Chrome, Edge, etc.) and Safari,\n * icons need to stay under <head> to be picked up by the browser.\n *\n */\nconst REINSERT_ICON_SCRIPT = `\\\ndocument.querySelectorAll('body link[rel=\"icon\"], body link[rel=\"apple-touch-icon\"]').forEach(el => document.head.appendChild(el))`\n\nexport function createServerInsertedMetadata(nonce: string | undefined) {\n  let inserted = false\n\n  return async function getServerInsertedMetadata(): Promise<string> {\n    if (inserted) {\n      return ''\n    }\n\n    inserted = true\n    return `<script ${nonce ? `nonce=\"${nonce}\"` : ''}>${REINSERT_ICON_SCRIPT}</script>`\n  }\n}\n"], "names": ["REINSERT_ICON_SCRIPT", "createServerInsertedMetadata", "nonce", "inserted", "getServerInsertedMetadata"], "mappings": "AAAA;;;;CAIC,GACD,MAAMA,uBAAuB,CAAC;kIACoG,CAAC;AAEnI,OAAO,SAASC,6BAA6BC,KAAyB;IACpE,IAAIC,WAAW;IAEf,OAAO,eAAeC;QACpB,IAAID,UAAU;YACZ,OAAO;QACT;QAEAA,WAAW;QACX,OAAO,CAAC,QAAQ,EAAED,QAAQ,CAAC,OAAO,EAAEA,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEF,qBAAqB,SAAS,CAAC;IACtF;AACF", "ignoreList": [0]}