'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Edit, Settings, FileText, Heart, Bookmark, Users } from 'lucide-react'
import { Header } from '@/components/layout/Header'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useAuth } from '@/contexts/AuthContext'

export default function ProfilePage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('posts')

  const tabs = [
    { id: 'posts', label: '我的文章', icon: FileText },
    { id: 'liked', label: '点赞的文章', icon: Heart },
    { id: 'bookmarked', label: '收藏的文章', icon: Bookmark },
    { id: 'following', label: '关注的人', icon: Users },
  ]

  const mockStats = {
    posts: 12,
    likes: 156,
    bookmarks: 23,
    following: 8,
    followers: 45
  }

  const mockPosts = [
    {
      id: '1',
      title: 'React 18 新特性深度解析',
      excerpt: '探索 React 18 带来的并发特性、自动批处理、Suspense 改进等重要更新...',
      publishedAt: '2024-01-15',
      likes: 42,
      comments: 12,
      status: 'published'
    },
    {
      id: '2',
      title: 'TypeScript 高级类型技巧',
      excerpt: '深入了解 TypeScript 的高级类型系统，包括条件类型、映射类型...',
      publishedAt: '2024-01-10',
      likes: 38,
      comments: 8,
      status: 'draft'
    }
  ]

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            请先登录
          </h1>
          <Button onClick={() => window.location.href = '/auth/login'}>
            去登录
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* 用户信息卡片 */}
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="flex items-center space-x-6">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-3xl font-bold text-white">
                    {user.email?.[0]?.toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {user.user_metadata?.full_name || '用户'}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {user.email}
                  </p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                    <div>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {mockStats.posts}
                      </span> 文章
                    </div>
                    <div>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {mockStats.followers}
                      </span> 粉丝
                    </div>
                    <div>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {mockStats.following}
                      </span> 关注
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Button variant="outline">
                    <Edit size={16} className="mr-2" />
                    编辑资料
                  </Button>
                  <Button variant="ghost">
                    <Settings size={16} />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 标签页导航 */}
          <div className="flex space-x-1 mb-8 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Icon size={16} />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* 内容区域 */}
          <div className="space-y-6">
            {activeTab === 'posts' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    我的文章
                  </h2>
                  <Button onClick={() => window.location.href = '/write'}>
                    写新文章
                  </Button>
                </div>
                
                <div className="space-y-4">
                  {mockPosts.map((post) => (
                    <Card key={post.id} className="hover:shadow-apple-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {post.title}
                              </h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                post.status === 'published'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              }`}>
                                {post.status === 'published' ? '已发布' : '草稿'}
                              </span>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 mb-3">
                              {post.excerpt}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                              <span>{post.publishedAt}</span>
                              <span>{post.likes} 点赞</span>
                              <span>{post.comments} 评论</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Button variant="ghost" size="sm">
                              编辑
                            </Button>
                            <Button variant="ghost" size="sm">
                              删除
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'liked' && (
              <div className="text-center py-12">
                <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无点赞的文章
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  去发现一些有趣的内容吧
                </p>
              </div>
            )}

            {activeTab === 'bookmarked' && (
              <div className="text-center py-12">
                <Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无收藏的文章
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  收藏喜欢的文章，方便以后查看
                </p>
              </div>
            )}

            {activeTab === 'following' && (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无关注的人
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  关注感兴趣的作者，获取他们的最新动态
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
