{"version": 3, "sources": ["../../../src/next-devtools/shared/forward-logs-shared.ts"], "sourcesContent": ["export type LogMethod =\n  | 'log'\n  | 'info'\n  | 'debug'\n  | 'table'\n  | 'error'\n  | 'assert'\n  | 'dir'\n  | 'dirxml'\n  | 'group'\n  | 'groupCollapsed'\n  | 'groupEnd'\n  | 'trace'\n  | 'warn'\n\nexport type ConsoleEntry<T> = {\n  kind: 'console'\n  method: LogMethod\n  consoleMethodStack: string | null\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string\n      }\n  >\n}\n\nexport type ConsoleErrorEntry<T> = {\n  kind: 'any-logged-error'\n  method: 'error'\n  consoleErrorStack: string\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n        isRejectionMessage?: boolean\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string | null\n      }\n  >\n}\n\nexport type FormattedErrorEntry = {\n  kind: 'formatted-error'\n  prefix: string\n  stack: string\n  method: 'error'\n}\n\nexport type ClientLogEntry =\n  | ConsoleEntry<unknown>\n  | ConsoleErrorEntry<unknown>\n  | FormattedErrorEntry\nexport type ServerLogEntry =\n  | ConsoleEntry<string>\n  | ConsoleErrorEntry<string>\n  | FormattedErrorEntry\n\nexport const UNDEFINED_MARKER = '__next_tagged_undefined'\n\n// Based on https://github.com/facebook/react/blob/28dc0776be2e1370fe217549d32aee2519f0cf05/packages/react-server/src/ReactFlightServer.js#L248\nexport function patchConsoleMethod<T extends keyof Console>(\n  methodName: T,\n  wrapper: (\n    methodName: T,\n    ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n  ) => void\n): () => void {\n  const descriptor = Object.getOwnPropertyDescriptor(console, methodName)\n  if (\n    descriptor &&\n    (descriptor.configurable || descriptor.writable) &&\n    typeof descriptor.value === 'function'\n  ) {\n    const originalMethod = descriptor.value as Console[T] extends (\n      ...args: any[]\n    ) => any\n      ? Console[T]\n      : never\n    const originalName = Object.getOwnPropertyDescriptor(originalMethod, 'name')\n    const wrapperMethod = function (\n      this: typeof console,\n      ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n    ) {\n      wrapper(methodName, ...args)\n\n      originalMethod.apply(this, args)\n    }\n    if (originalName) {\n      Object.defineProperty(wrapperMethod, 'name', originalName)\n    }\n    Object.defineProperty(console, methodName, {\n      value: wrapperMethod,\n    })\n\n    return () => {\n      Object.defineProperty(console, methodName, {\n        value: originalMethod,\n        writable: descriptor.writable,\n        configurable: descriptor.configurable,\n      })\n    }\n  }\n\n  return () => {}\n}\n"], "names": ["UNDEFINED_MARKER", "patchConsoleMethod", "methodName", "wrapper", "descriptor", "Object", "getOwnPropertyDescriptor", "console", "configurable", "writable", "value", "originalMethod", "originalName", "wrapperMethod", "args", "apply", "defineProperty"], "mappings": "AAkEA,OAAO,MAAMA,mBAAmB,0BAAyB;AAEzD,+IAA+I;AAC/I,OAAO,SAASC,mBACdC,UAAa,EACbC,OAGS;IAET,MAAMC,aAAaC,OAAOC,wBAAwB,CAACC,SAASL;IAC5D,IACEE,cACCA,CAAAA,WAAWI,YAAY,IAAIJ,WAAWK,QAAQ,AAAD,KAC9C,OAAOL,WAAWM,KAAK,KAAK,YAC5B;QACA,MAAMC,iBAAiBP,WAAWM,KAAK;QAKvC,MAAME,eAAeP,OAAOC,wBAAwB,CAACK,gBAAgB;QACrE,MAAME,gBAAgB;YAEpB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;gBAAGA,KAAH,QAAA,SAAA,CAAA,KAAmE;;YAEnEX,QAAQD,eAAeY;YAEvBH,eAAeI,KAAK,CAAC,IAAI,EAAED;QAC7B;QACA,IAAIF,cAAc;YAChBP,OAAOW,cAAc,CAACH,eAAe,QAAQD;QAC/C;QACAP,OAAOW,cAAc,CAACT,SAASL,YAAY;YACzCQ,OAAOG;QACT;QAEA,OAAO;YACLR,OAAOW,cAAc,CAACT,SAASL,YAAY;gBACzCQ,OAAOC;gBACPF,UAAUL,WAAWK,QAAQ;gBAC7BD,cAAcJ,WAAWI,YAAY;YACvC;QACF;IACF;IAEA,OAAO,KAAO;AAChB", "ignoreList": [0]}