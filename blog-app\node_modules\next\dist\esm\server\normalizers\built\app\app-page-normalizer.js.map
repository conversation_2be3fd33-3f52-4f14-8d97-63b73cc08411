{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-page-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\nimport { Normalizers } from '../../normalizers'\n\n/**\n * DevAppPageNormalizer is a normalizer that is used to normalize a pathname\n * to a page in the `app` directory.\n */\nclass DevAppPageNormalizerInternal extends AbsoluteFilenameNormalizer {\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    super(appDir, extensions, PAGE_TYPES.APP)\n  }\n}\n\nexport class DevAppPageNormalizer extends Normalizers {\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    _isTurbopack: boolean\n  ) {\n    const normalizer = new DevAppPageNormalizerInternal(appDir, extensions)\n    super(\n      // %5F to _ replacement should only happen with Turbopack.\n      // TODO: enable when page matcher `/_` check is moved: https://github.com/vercel/next.js/blob/8eda00bf5999e43e8f0211bd72c981d5ce292e8b/packages/next/src/server/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts#L48\n      // isTurbopack\n      //   ? [\n      //       // The page should have the `%5F` characters replaced with `_` characters.\n      //       new UnderscoreNormalizer(),\n      //       normalizer,\n      //     ]\n      //   : [normalizer]\n      [normalizer]\n    )\n  }\n}\n"], "names": ["PAGE_TYPES", "AbsoluteFilenameNormalizer", "Normalizers", "DevAppPageNormalizerInternal", "constructor", "appDir", "extensions", "APP", "DevAppPageNormalizer", "_isTurbopack", "normalizer"], "mappings": "AAAA,SAASA,UAAU,QAAQ,6BAA4B;AACvD,SAASC,0BAA0B,QAAQ,qCAAoC;AAC/E,SAASC,WAAW,QAAQ,oBAAmB;AAE/C;;;CAGC,GACD,MAAMC,qCAAqCF;IACzCG,YAAYC,MAAc,EAAEC,UAAiC,CAAE;QAC7D,KAAK,CAACD,QAAQC,YAAYN,WAAWO,GAAG;IAC1C;AACF;AAEA,OAAO,MAAMC,6BAA6BN;IACxCE,YACEC,MAAc,EACdC,UAAiC,EACjCG,YAAqB,CACrB;QACA,MAAMC,aAAa,IAAIP,6BAA6BE,QAAQC;QAC5D,KAAK,CACH,0DAA0D;QAC1D,uOAAuO;QACvO,cAAc;QACd,QAAQ;QACR,mFAAmF;QACnF,oCAAoC;QACpC,oBAAoB;QACpB,QAAQ;QACR,mBAAmB;QACnB;YAACI;SAAW;IAEhB;AACF", "ignoreList": [0]}