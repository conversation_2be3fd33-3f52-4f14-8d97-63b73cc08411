{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "sourcesContent": ["import type { CacheNodeSeedData, PreloadCallbacks } from './types'\nimport React from 'react'\nimport {\n  isClientReference,\n  isUseCacheFunction,\n} from '../../lib/client-and-server-references'\nimport { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { interopDefault } from './interop-default'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport type { AppRenderContext, GetDynamicParamFromSegment } from './app-render'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { getLayerAssets } from './get-layer-assets'\nimport { hasLoadingComponentInTree } from './has-loading-component-in-tree'\nimport { validateRevalidate } from '../lib/patch-fetch'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../client/components/builtin/default'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NextNodeServerSpan } from '../lib/trace/constants'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { Params } from '../request/params'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { OUTLET_BOUNDARY_NAME } from '../../lib/metadata/metadata-constants'\nimport type {\n  UseCacheLayoutComponentProps,\n  UseCachePageComponentProps,\n} from '../use-cache/use-cache-wrapper'\nimport { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport {\n  BOUNDARY_PREFIX,\n  BOUNDARY_SUFFIX,\n  getConventionPathByType,\n} from './segment-explorer-path'\n\n/**\n * Use the provided loader tree to create the React Component tree.\n */\nexport function createComponentTree(props: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  return getTracer().trace(\n    NextNodeServerSpan.createComponentTree,\n    {\n      spanName: 'build component tree',\n    },\n    () => createComponentTreeInternal(props)\n  )\n}\n\nfunction errorMissingDefaultExport(\n  pagePath: string,\n  convention: string\n): never {\n  const normalizedPagePath = pagePath === '/' ? '' : pagePath\n  throw new Error(\n    `The default export is not a React Component in \"${normalizedPagePath}/${convention}\"`\n  )\n}\n\nconst cacheNodeKey = 'c'\n\nasync function createComponentTreeInternal({\n  loaderTree: tree,\n  parentParams,\n  rootLayoutIncluded,\n  injectedCSS,\n  injectedJS,\n  injectedFontPreloadTags,\n  getViewportReady,\n  getMetadataReady,\n  ctx,\n  missingSlots,\n  preloadCallbacks,\n  authInterrupts,\n  StreamingMetadataOutlet,\n}: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getViewportReady: () => Promise<void>\n  getMetadataReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  const {\n    renderOpts: { nextConfigOutput, experimental },\n    workStore,\n    componentMod: {\n      SegmentViewNode,\n      HTTPAccessFallbackBoundary,\n      LayoutRouter,\n      RenderFromTemplateContext,\n      OutletBoundary,\n      ClientPageRoot,\n      ClientSegmentRoot,\n      createServerSearchParamsForServerPage,\n      createPrerenderSearchParamsForClientPage,\n      createServerParamsForServerSegment,\n      createPrerenderParamsForClientSegment,\n      serverHooks: { DynamicServerError },\n      Postpone,\n    },\n    pagePath,\n    getDynamicParamFromSegment,\n    isPrefetch,\n    query,\n  } = ctx\n\n  const { page, conventionPath, segment, modules, parallelRoutes } =\n    parseLoaderTree(tree)\n\n  const {\n    layout,\n    template,\n    error,\n    loading,\n    'not-found': notFound,\n    forbidden,\n    unauthorized,\n  } = modules\n\n  const injectedCSSWithCurrentLayout = new Set(injectedCSS)\n  const injectedJSWithCurrentLayout = new Set(injectedJS)\n  const injectedFontPreloadTagsWithCurrentLayout = new Set(\n    injectedFontPreloadTags\n  )\n\n  const layerAssets = getLayerAssets({\n    preloadCallbacks,\n    ctx,\n    layoutOrPagePath: conventionPath,\n    injectedCSS: injectedCSSWithCurrentLayout,\n    injectedJS: injectedJSWithCurrentLayout,\n    injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  })\n\n  const [Template, templateStyles, templateScripts] = template\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: template[1],\n        getComponent: template[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : [React.Fragment]\n\n  const [ErrorComponent, errorStyles, errorScripts] = error\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: error[1],\n        getComponent: error[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Loading, loadingStyles, loadingScripts] = loading\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: loading[1],\n        getComponent: loading[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const { mod: layoutOrPageMod, modType } = await getTracer().trace(\n    NextNodeServerSpan.getLayoutOrPageModule,\n    {\n      hideSpan: !(isLayout || isPage),\n      spanName: 'resolve segment modules',\n      attributes: {\n        'next.segment': segment,\n      },\n    },\n    () => getLayoutOrPageModule(tree)\n  )\n\n  const gracefullyDegrade = !!ctx.renderOpts.botType\n\n  /**\n   * Checks if the current segment is a root layout.\n   */\n  const rootLayoutAtThisLevel = isLayout && !rootLayoutIncluded\n  /**\n   * Checks if the current segment or any level above it has a root layout.\n   */\n  const rootLayoutIncludedAtThisLevelOrAbove =\n    rootLayoutIncluded || rootLayoutAtThisLevel\n\n  const [NotFound, notFoundStyles] = notFound\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: notFound[1],\n        getComponent: notFound[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Forbidden, forbiddenStyles] =\n    authInterrupts && forbidden\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: forbidden[1],\n          getComponent: forbidden[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  const [Unauthorized, unauthorizedStyles] =\n    authInterrupts && unauthorized\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: unauthorized[1],\n          getComponent: unauthorized[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  let dynamic = layoutOrPageMod?.dynamic\n\n  if (nextConfigOutput === 'export') {\n    if (!dynamic || dynamic === 'auto') {\n      dynamic = 'error'\n    } else if (dynamic === 'force-dynamic') {\n      // force-dynamic is always incompatible with 'export'. We must interrupt the build\n      throw new StaticGenBailoutError(\n        `Page with \\`dynamic = \"force-dynamic\"\\` couldn't be exported. \\`output: \"export\"\\` requires all pages be renderable statically because there is no runtime server to dynamically render routes in this output format. Learn more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports`\n      )\n    }\n  }\n\n  if (typeof dynamic === 'string') {\n    // the nested most config wins so we only force-static\n    // if it's configured above any parent that configured\n    // otherwise\n    if (dynamic === 'error') {\n      workStore.dynamicShouldError = true\n    } else if (dynamic === 'force-dynamic') {\n      workStore.forceDynamic = true\n\n      // TODO: (PPR) remove this bailout once PPR is the default\n      if (workStore.isStaticGeneration && !experimental.isRoutePPREnabled) {\n        // If the postpone API isn't available, we can't postpone the render and\n        // therefore we can't use the dynamic API.\n        const err = new DynamicServerError(\n          `Page with \\`dynamic = \"force-dynamic\"\\` won't be rendered statically.`\n        )\n        workStore.dynamicUsageDescription = err.message\n        workStore.dynamicUsageStack = err.stack\n        throw err\n      }\n    } else {\n      workStore.dynamicShouldError = false\n      workStore.forceStatic = dynamic === 'force-static'\n    }\n  }\n\n  if (typeof layoutOrPageMod?.fetchCache === 'string') {\n    workStore.fetchCache = layoutOrPageMod?.fetchCache\n  }\n\n  if (typeof layoutOrPageMod?.revalidate !== 'undefined') {\n    validateRevalidate(layoutOrPageMod?.revalidate, workStore.route)\n  }\n\n  if (typeof layoutOrPageMod?.revalidate === 'number') {\n    const defaultRevalidate = layoutOrPageMod.revalidate as number\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (workUnitStore) {\n      if (\n        workUnitStore.type === 'prerender' ||\n        workUnitStore.type === 'prerender-legacy' ||\n        workUnitStore.type === 'prerender-ppr' ||\n        workUnitStore.type === 'cache'\n      ) {\n        if (workUnitStore.revalidate > defaultRevalidate) {\n          workUnitStore.revalidate = defaultRevalidate\n        }\n      }\n    }\n\n    if (\n      !workStore.forceStatic &&\n      workStore.isStaticGeneration &&\n      defaultRevalidate === 0 &&\n      // If the postpone API isn't available, we can't postpone the render and\n      // therefore we can't use the dynamic API.\n      !experimental.isRoutePPREnabled\n    ) {\n      const dynamicUsageDescription = `revalidate: 0 configured ${segment}`\n      workStore.dynamicUsageDescription = dynamicUsageDescription\n\n      throw new DynamicServerError(dynamicUsageDescription)\n    }\n  }\n\n  const isStaticGeneration = workStore.isStaticGeneration\n\n  // Assume the segment we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // It's OK for this to be `true` when the data is actually fully static, but\n  // it's not OK for this to be `false` when the data possibly contains holes.\n  // Although the value here is overly pessimistic, for prefetches, it will be\n  // replaced by a more specific value when the data is later processed into\n  // per-segment responses (see collect-segment-data.tsx)\n  //\n  // For dynamic requests, this must always be `false` because dynamic responses\n  // are never partial.\n  const isPossiblyPartialResponse =\n    isStaticGeneration && experimental.isRoutePPREnabled === true\n\n  const LayoutOrPage: React.ComponentType<any> | undefined = layoutOrPageMod\n    ? interopDefault(layoutOrPageMod)\n    : undefined\n\n  /**\n   * The React Component to render.\n   */\n  let MaybeComponent = LayoutOrPage\n\n  if (process.env.NODE_ENV === 'development') {\n    const { isValidElementType } =\n      require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n    if (\n      typeof MaybeComponent !== 'undefined' &&\n      !isValidElementType(MaybeComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, modType ?? 'page')\n    }\n\n    if (\n      typeof ErrorComponent !== 'undefined' &&\n      !isValidElementType(ErrorComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, 'error')\n    }\n\n    if (typeof Loading !== 'undefined' && !isValidElementType(Loading)) {\n      errorMissingDefaultExport(pagePath, 'loading')\n    }\n\n    if (typeof NotFound !== 'undefined' && !isValidElementType(NotFound)) {\n      errorMissingDefaultExport(pagePath, 'not-found')\n    }\n\n    if (typeof Forbidden !== 'undefined' && !isValidElementType(Forbidden)) {\n      errorMissingDefaultExport(pagePath, 'forbidden')\n    }\n\n    if (\n      typeof Unauthorized !== 'undefined' &&\n      !isValidElementType(Unauthorized)\n    ) {\n      errorMissingDefaultExport(pagePath, 'unauthorized')\n    }\n  }\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  // Create object holding the parent params and current params\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  // Resolve the segment param\n  const actualSegment = segmentParam ? segmentParam.treeSegment : segment\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR!\n      : ctx.renderOpts.dir || ''\n\n  // Use the same condition to render metadataOutlet as metadata\n  const metadataOutlet = StreamingMetadataOutlet ? (\n    <StreamingMetadataOutlet />\n  ) : (\n    <MetadataOutlet ready={getMetadataReady} />\n  )\n\n  const [notFoundElement, notFoundFilePath] =\n    await createBoundaryConventionElement({\n      ctx,\n      conventionName: 'not-found',\n      Component: NotFound,\n      styles: notFoundStyles,\n      tree,\n    })\n\n  const [forbiddenElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'forbidden',\n    Component: Forbidden,\n    styles: forbiddenStyles,\n    tree,\n  })\n\n  const [unauthorizedElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'unauthorized',\n    Component: Unauthorized,\n    styles: unauthorizedStyles,\n    tree,\n  })\n\n  // TODO: Combine this `map` traversal with the loop below that turns the array\n  // into an object.\n  const parallelRouteMap = await Promise.all(\n    Object.keys(parallelRoutes).map(\n      async (\n        parallelRouteKey\n      ): Promise<[string, React.ReactNode, CacheNodeSeedData | null]> => {\n        const isChildrenRouteKey = parallelRouteKey === 'children'\n        const parallelRoute = parallelRoutes[parallelRouteKey]\n\n        const notFoundComponent = isChildrenRouteKey\n          ? notFoundElement\n          : undefined\n\n        const forbiddenComponent = isChildrenRouteKey\n          ? forbiddenElement\n          : undefined\n\n        const unauthorizedComponent = isChildrenRouteKey\n          ? unauthorizedElement\n          : undefined\n\n        // if we're prefetching and that there's a Loading component, we bail out\n        // otherwise we keep rendering for the prefetch.\n        // We also want to bail out if there's no Loading component in the tree.\n        let childCacheNodeSeedData: CacheNodeSeedData | null = null\n\n        if (\n          // Before PPR, the way instant navigations work in Next.js is we\n          // prefetch everything up to the first route segment that defines a\n          // loading.tsx boundary. (We do the same if there's no loading\n          // boundary in the entire tree, because we don't want to prefetch too\n          // much) The rest of the tree is deferred until the actual navigation.\n          // It does not take into account whether the data is dynamic — even if\n          // the tree is completely static, it will still defer everything\n          // inside the loading boundary.\n          //\n          // This behavior predates PPR and is only relevant if the\n          // PPR flag is not enabled.\n          isPrefetch &&\n          (Loading || !hasLoadingComponentInTree(parallelRoute)) &&\n          // The approach with PPR is different — loading.tsx behaves like a\n          // regular Suspense boundary and has no special behavior.\n          //\n          // With PPR, we prefetch as deeply as possible, and only defer when\n          // dynamic data is accessed. If so, we only defer the nearest parent\n          // Suspense boundary of the dynamic data access, regardless of whether\n          // the boundary is defined by loading.tsx or a normal <Suspense>\n          // component in userspace.\n          //\n          // NOTE: In practice this usually means we'll end up prefetching more\n          // than we were before PPR, which may or may not be considered a\n          // performance regression by some apps. The plan is to address this\n          // before General Availability of PPR by introducing granular\n          // per-segment fetching, so we can reuse as much of the tree as\n          // possible during both prefetches and dynamic navigations. But during\n          // the beta period, we should be clear about this trade off in our\n          // communications.\n          !experimental.isRoutePPREnabled\n        ) {\n          // Don't prefetch this child. This will trigger a lazy fetch by the\n          // client router.\n        } else {\n          // Create the child component\n\n          if (process.env.NODE_ENV === 'development' && missingSlots) {\n            // When we detect the default fallback (which triggers a 404), we collect the missing slots\n            // to provide more helpful debug information during development mode.\n            const parsedTree = parseLoaderTree(parallelRoute)\n            if (\n              parsedTree.conventionPath?.endsWith(PARALLEL_ROUTE_DEFAULT_PATH)\n            ) {\n              missingSlots.add(parallelRouteKey)\n            }\n          }\n\n          const seedData = await createComponentTreeInternal({\n            loaderTree: parallelRoute,\n            parentParams: currentParams,\n            rootLayoutIncluded: rootLayoutIncludedAtThisLevelOrAbove,\n            injectedCSS: injectedCSSWithCurrentLayout,\n            injectedJS: injectedJSWithCurrentLayout,\n            injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n            // `getMetadataReady` and `getViewportReady` are used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            getMetadataReady: isChildrenRouteKey\n              ? getMetadataReady\n              : () => Promise.resolve(),\n            getViewportReady: isChildrenRouteKey\n              ? getViewportReady\n              : () => Promise.resolve(),\n            ctx,\n            missingSlots,\n            preloadCallbacks,\n            authInterrupts,\n            // `StreamingMetadataOutlet` is used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            StreamingMetadataOutlet: isChildrenRouteKey\n              ? StreamingMetadataOutlet\n              : null,\n          })\n\n          childCacheNodeSeedData = seedData\n        }\n\n        const templateNode = (\n          <Template>\n            <RenderFromTemplateContext />\n          </Template>\n        )\n\n        const templateFilePath = getConventionPathByType(tree, dir, 'template')\n        const errorFilePath = getConventionPathByType(tree, dir, 'error')\n        const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n\n        const wrappedErrorStyles =\n          isSegmentViewEnabled && errorFilePath ? (\n            <SegmentViewNode type=\"error\" pagePath={errorFilePath}>\n              {errorStyles}\n            </SegmentViewNode>\n          ) : (\n            errorStyles\n          )\n\n        // Add a suffix to avoid conflict with the segment view node representing rendered file.\n        // existence: not-found.tsx@boundary\n        // rendered: not-found.tsx\n        const fileNameSuffix = BOUNDARY_SUFFIX\n        const segmentViewBoundaries = isSegmentViewEnabled ? (\n          <>\n            {notFoundFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}not-found`}\n                pagePath={notFoundFilePath + fileNameSuffix}\n              />\n            )}\n            {loadingFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}loading`}\n                pagePath={loadingFilePath + fileNameSuffix}\n              />\n            )}\n            {errorFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}error`}\n                pagePath={errorFilePath + fileNameSuffix}\n              />\n            )}\n            {/* do not surface forbidden and unauthorized boundaries yet as they're unstable */}\n          </>\n        ) : null\n\n        return [\n          parallelRouteKey,\n          <LayoutRouter\n            parallelRouterKey={parallelRouteKey}\n            // TODO-APP: Add test for loading returning `undefined`. This currently can't be tested as the `webdriver()` tab will wait for the full page to load before returning.\n            error={ErrorComponent}\n            errorStyles={wrappedErrorStyles}\n            errorScripts={errorScripts}\n            template={\n              // Only render SegmentViewNode when there's an actual template\n              isSegmentViewEnabled && templateFilePath ? (\n                <SegmentViewNode type=\"template\" pagePath={templateFilePath}>\n                  {templateNode}\n                </SegmentViewNode>\n              ) : (\n                templateNode\n              )\n            }\n            templateStyles={templateStyles}\n            templateScripts={templateScripts}\n            notFound={notFoundComponent}\n            forbidden={forbiddenComponent}\n            unauthorized={unauthorizedComponent}\n            {...(isSegmentViewEnabled && { segmentViewBoundaries })}\n            // Since gracefullyDegrade only applies to bots, only\n            // pass it when we're in a bot context to avoid extra bytes.\n            {...(gracefullyDegrade && { gracefullyDegrade })}\n          />,\n          childCacheNodeSeedData,\n        ]\n      }\n    )\n  )\n\n  // Convert the parallel route map into an object after all promises have been resolved.\n  let parallelRouteProps: { [key: string]: React.ReactNode } = {}\n  let parallelRouteCacheNodeSeedData: {\n    [key: string]: CacheNodeSeedData | null\n  } = {}\n  for (const parallelRoute of parallelRouteMap) {\n    const [parallelRouteKey, parallelRouteProp, flightData] = parallelRoute\n    parallelRouteProps[parallelRouteKey] = parallelRouteProp\n    parallelRouteCacheNodeSeedData[parallelRouteKey] = flightData\n  }\n\n  let loadingElement = Loading ? <Loading key=\"l\" /> : null\n  const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n  if (isSegmentViewEnabled && loadingElement) {\n    if (loadingFilePath) {\n      loadingElement = (\n        <SegmentViewNode\n          key={cacheNodeKey + '-loading'}\n          type=\"loading\"\n          pagePath={loadingFilePath}\n        >\n          {loadingElement}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  const loadingData: LoadingModuleData = loadingElement\n    ? [loadingElement, loadingStyles, loadingScripts]\n    : null\n\n  // When the segment does not have a layout or page we still have to add the layout router to ensure the path holds the loading component\n  if (!MaybeComponent) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {layerAssets}\n        {parallelRouteProps.children}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n\n  const Component = MaybeComponent\n  // If force-dynamic is used and the current render supports postponing, we\n  // replace it with a node that will postpone the render. This ensures that the\n  // postpone is invoked during the react render phase and not during the next\n  // render phase.\n  // @TODO this does not actually do what it seems like it would or should do. The idea is that\n  // if we are rendering in a force-dynamic mode and we can postpone we should only make the segments\n  // that ask for force-dynamic to be dynamic, allowing other segments to still prerender. However\n  // because this comes after the children traversal and the static generation store is mutated every segment\n  // along the parent path of a force-dynamic segment will hit this condition effectively making the entire\n  // render force-dynamic. We should refactor this function so that we can correctly track which segments\n  // need to be dynamic\n  if (\n    workStore.isStaticGeneration &&\n    workStore.forceDynamic &&\n    experimental.isRoutePPREnabled\n  ) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        <Postpone\n          reason='dynamic = \"force-dynamic\" was used'\n          route={workStore.route}\n        />\n        {layerAssets}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      true,\n    ]\n  }\n\n  const isClientComponent = isClientReference(layoutOrPageMod)\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    'params' in parallelRouteProps\n  ) {\n    // @TODO consider making this an error and running the check in build as well\n    console.error(\n      `\"params\" is a reserved prop in Layouts and Pages and cannot be used as the name of a parallel route in ${segment}`\n    )\n  }\n\n  if (isPage) {\n    const PageComponent = Component\n\n    // Assign searchParams to props if this is a page\n    let pageElement: React.ReactNode\n    if (isClientComponent) {\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n        const promiseOfSearchParams =\n          createPrerenderSearchParamsForClientPage(workStore)\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n            promises={[promiseOfSearchParams, promiseOfParams]}\n          />\n        )\n      } else {\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n          />\n        )\n      }\n    } else {\n      // If we are passing params to a server component Page we need to track\n      // their usage in case the current render mode tracks dynamic API usage.\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      // If we are passing searchParams to a server component Page we need to\n      // track their usage in case the current render mode tracks dynamic API\n      // usage.\n      let searchParams = createServerSearchParamsForServerPage(query, workStore)\n\n      if (isUseCacheFunction(PageComponent)) {\n        const UseCachePageComponent: React.ComponentType<UseCachePageComponentProps> =\n          PageComponent\n\n        if (!experimental.dynamicIO) {\n          // The \"use cache\" wrapper takes care of converting this into an\n          // erroring search params promise when passing it to the original\n          // function.\n          searchParams = Promise.resolve({})\n        }\n\n        pageElement = (\n          <UseCachePageComponent\n            params={params}\n            searchParams={searchParams}\n            $$isPageComponent\n          />\n        )\n      } else {\n        pageElement = (\n          <PageComponent params={params} searchParams={searchParams} />\n        )\n      }\n    }\n\n    const isDefaultSegment = segment === DEFAULT_SEGMENT_KEY\n    const pageFilePath =\n      getConventionPathByType(tree, dir, 'page') ??\n      getConventionPathByType(tree, dir, 'defaultPage')\n    const segmentType = isDefaultSegment ? 'default' : 'page'\n    const wrappedPageElement =\n      isSegmentViewEnabled && pageFilePath ? (\n        <SegmentViewNode\n          key={cacheNodeKey + '-' + segmentType}\n          type={segmentType}\n          pagePath={pageFilePath}\n        >\n          {pageElement}\n        </SegmentViewNode>\n      ) : (\n        pageElement\n      )\n\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {wrappedPageElement}\n        {layerAssets}\n        <OutletBoundary>\n          <MetadataOutlet ready={getViewportReady} />\n          {metadataOutlet}\n        </OutletBoundary>\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  } else {\n    const SegmentComponent = Component\n    const isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot =\n      rootLayoutAtThisLevel &&\n      'children' in parallelRoutes &&\n      Object.keys(parallelRoutes).length > 1\n\n    let segmentNode: React.ReactNode\n\n    if (isClientComponent) {\n      let clientSegment: React.ReactNode\n\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n            promise={promiseOfParams}\n          />\n        )\n      } else {\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n          />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        let notfoundClientSegment: React.ReactNode\n        let forbiddenClientSegment: React.ReactNode\n        let unauthorizedClientSegment: React.ReactNode\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        notfoundClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: NotFound,\n          errorElement: notFoundElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        forbiddenClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Forbidden,\n          errorElement: forbiddenElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        unauthorizedClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Unauthorized,\n          errorElement: unauthorizedElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        if (\n          notfoundClientSegment ||\n          forbiddenClientSegment ||\n          unauthorizedClientSegment\n        ) {\n          segmentNode = (\n            <HTTPAccessFallbackBoundary\n              key={cacheNodeKey}\n              notFound={notfoundClientSegment}\n              forbidden={forbiddenClientSegment}\n              unauthorized={unauthorizedClientSegment}\n            >\n              {layerAssets}\n              {clientSegment}\n            </HTTPAccessFallbackBoundary>\n          )\n        } else {\n          segmentNode = (\n            <React.Fragment key={cacheNodeKey}>\n              {layerAssets}\n              {clientSegment}\n            </React.Fragment>\n          )\n        }\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {clientSegment}\n          </React.Fragment>\n        )\n      }\n    } else {\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      let serverSegment: React.ReactNode\n\n      if (isUseCacheFunction(SegmentComponent)) {\n        const UseCacheLayoutComponent: React.ComponentType<UseCacheLayoutComponentProps> =\n          SegmentComponent\n\n        serverSegment = (\n          <UseCacheLayoutComponent\n            {...parallelRouteProps}\n            params={params}\n            $$isLayoutComponent\n          />\n        )\n      } else {\n        serverSegment = (\n          <SegmentComponent {...parallelRouteProps} params={params} />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        segmentNode = (\n          <HTTPAccessFallbackBoundary\n            key={cacheNodeKey}\n            notFound={\n              notFoundElement ? (\n                <>\n                  {layerAssets}\n                  <SegmentComponent params={params}>\n                    {notFoundStyles}\n                    {notFoundElement}\n                  </SegmentComponent>\n                </>\n              ) : undefined\n            }\n          >\n            {layerAssets}\n            {serverSegment}\n          </HTTPAccessFallbackBoundary>\n        )\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {serverSegment}\n          </React.Fragment>\n        )\n      }\n    }\n\n    const layoutFilePath = getConventionPathByType(tree, dir, 'layout')\n    const wrappedSegmentNode =\n      isSegmentViewEnabled && layoutFilePath ? (\n        <SegmentViewNode key=\"layout\" type=\"layout\" pagePath={layoutFilePath}>\n          {segmentNode}\n        </SegmentViewNode>\n      ) : (\n        segmentNode\n      )\n\n    // For layouts we just render the component\n    return [\n      actualSegment,\n      wrappedSegmentNode,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n}\n\nasync function MetadataOutlet({\n  ready,\n}: {\n  ready: () => Promise<void> & { status?: string; value?: unknown }\n}) {\n  const r = ready()\n  // We can avoid a extra microtask by unwrapping the instrumented promise directly if available.\n  if (r.status === 'rejected') {\n    throw r.value\n  } else if (r.status !== 'fulfilled') {\n    await r\n  }\n  return null\n}\nMetadataOutlet.displayName = OUTLET_BOUNDARY_NAME\n\nfunction createErrorBoundaryClientSegmentRoot({\n  ErrorBoundaryComponent,\n  errorElement,\n  ClientSegmentRoot,\n  layerAssets,\n  SegmentComponent,\n  currentParams,\n}: {\n  ErrorBoundaryComponent: React.ComponentType<any> | undefined\n  errorElement: React.ReactNode\n  ClientSegmentRoot: React.ComponentType<any>\n  layerAssets: React.ReactNode\n  SegmentComponent: React.ComponentType<any>\n  currentParams: Params\n}) {\n  if (ErrorBoundaryComponent) {\n    const notFoundParallelRouteProps = {\n      children: errorElement,\n    }\n    return (\n      <>\n        {layerAssets}\n        <ClientSegmentRoot\n          Component={SegmentComponent}\n          slots={notFoundParallelRouteProps}\n          params={currentParams}\n        />\n      </>\n    )\n  }\n  return null\n}\n\nexport function getRootParams(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  return getRootParamsImpl({}, loaderTree, getDynamicParamFromSegment)\n}\n\nfunction getRootParamsImpl(\n  parentParams: Params,\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  const {\n    segment,\n    modules: { layout },\n    parallelRoutes,\n  } = parseLoaderTree(loaderTree)\n\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const isRootLayout = typeof layout !== 'undefined'\n\n  if (isRootLayout) {\n    return currentParams\n  } else if (!parallelRoutes.children) {\n    // This should really be an error but there are bugs in Turbopack that cause\n    // the _not-found LoaderTree to not have any layouts. For rootParams sake\n    // this is somewhat irrelevant when you are not customizing the 404 page.\n    // If you are customizing 404\n    // TODO update rootParams to make all params optional if `/app/not-found.tsx` is defined\n    return currentParams\n  } else {\n    return getRootParamsImpl(\n      currentParams,\n      // We stop looking for root params as soon as we hit the first layout\n      // and it is not possible to use parallel route children above the root layout\n      // so every parallelRoutes object that this function can visit will necessarily\n      // have a single `children` prop and no others.\n      parallelRoutes.children,\n      getDynamicParamFromSegment\n    )\n  }\n}\n\nasync function createBoundaryConventionElement({\n  ctx,\n  conventionName,\n  Component,\n  styles,\n  tree,\n}: {\n  ctx: AppRenderContext\n  conventionName:\n    | 'not-found'\n    | 'error'\n    | 'loading'\n    | 'forbidden'\n    | 'unauthorized'\n  Component: React.ComponentType<any> | undefined\n  styles: React.ReactNode | undefined\n  tree: LoaderTree\n}) {\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR!\n      : ctx.renderOpts.dir || ''\n  const { SegmentViewNode } = ctx.componentMod\n  const element = Component ? (\n    <>\n      <Component />\n      {styles}\n    </>\n  ) : undefined\n\n  const pagePath = getConventionPathByType(tree, dir, conventionName)\n\n  const wrappedElement =\n    isSegmentViewEnabled && element ? (\n      <SegmentViewNode\n        key={cacheNodeKey + '-' + conventionName}\n        type={conventionName}\n        pagePath={pagePath!}\n      >\n        {element}\n      </SegmentViewNode>\n    ) : (\n      element\n    )\n\n  return [wrappedElement, pagePath] as const\n}\n"], "names": ["createComponentTree", "getRootParams", "props", "getTracer", "trace", "NextNodeServerSpan", "spanName", "createComponentTreeInternal", "errorMissingDefaultExport", "pagePath", "convention", "normalizedPagePath", "Error", "cacheNodeKey", "loaderTree", "tree", "parentParams", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "getViewportReady", "getMetadataReady", "ctx", "missingSlots", "preloadCallbacks", "authInterrupts", "StreamingMetadataOutlet", "renderOpts", "nextConfigOutput", "experimental", "workStore", "componentMod", "SegmentViewNode", "HTTPAccessFallbackBoundary", "LayoutRouter", "RenderFromTemplateContext", "OutletBoundary", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "DynamicServerError", "Postpone", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "conventionPath", "segment", "modules", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "forbidden", "unauthorized", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "layoutOrPagePath", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "mod", "layoutOrPageMod", "modType", "getLayoutOrPageModule", "hideSpan", "attributes", "gracefully<PERSON><PERSON><PERSON>", "botType", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "Forbidden", "forbiddenStyles", "Unauthorized", "unauthorizedStyles", "dynamic", "StaticGenBailoutError", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "isRoutePPREnabled", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "validateRevalidate", "route", "defaultRevalidate", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "isPossiblyPartialResponse", "LayoutOrPage", "interopDefault", "undefined", "MaybeComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "isSegmentViewEnabled", "devtoolSegmentExplorer", "dir", "NEXT_RUNTIME", "__NEXT_EDGE_PROJECT_DIR", "metadataOutlet", "MetadataOutlet", "ready", "notFoundElement", "notFoundFilePath", "createBoundaryConventionElement", "conventionName", "Component", "styles", "forbiddenElement", "unauthorizedElement", "parallelRouteMap", "Promise", "all", "Object", "keys", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "parallelRoute", "notFoundComponent", "forbiddenComponent", "unauthorizedComponent", "childCacheNodeSeedData", "hasLoadingComponentInTree", "parsedTree", "endsWith", "PARALLEL_ROUTE_DEFAULT_PATH", "add", "seedData", "resolve", "templateNode", "templateFilePath", "getConventionPathByType", "errorFilePath", "loadingFilePath", "wrappedErrorStyles", "fileNameSuffix", "BOUNDARY_SUFFIX", "segmentViewBoundaries", "BOUNDARY_PREFIX", "parallel<PERSON><PERSON>er<PERSON>ey", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingElement", "loadingData", "children", "reason", "isClientComponent", "isClientReference", "console", "PageComponent", "pageElement", "promiseOfParams", "promiseOfSearchParams", "searchParams", "params", "promises", "isUseCacheFunction", "UseCachePageComponent", "dynamicIO", "$$isPageComponent", "isDefaultSegment", "DEFAULT_SEGMENT_KEY", "pageFilePath", "segmentType", "wrappedPageElement", "SegmentComponent", "isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot", "length", "segmentNode", "clientSegment", "slots", "promise", "notfoundClientSegment", "forbiddenClientSegment", "unauthorizedClientSegment", "createErrorBoundaryClientSegmentRoot", "ErrorBoundaryComponent", "errorElement", "serverSegment", "UseCacheLayoutComponent", "$$isLayoutComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wrappedSegmentNode", "r", "status", "displayName", "OUTLET_BOUNDARY_NAME", "notFoundParallelRouteProps", "getRootParamsImpl", "isRootLayout", "element", "wrappedElement"], "mappings": ";;;;;;;;;;;;;;;IAqCgBA,mBAAmB;eAAnBA;;IAi/BAC,aAAa;eAAbA;;;;8DArhCE;2CAIX;8BAC+B;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;4BACP;yBACS;wBAClB;2BACS;yCACG;8CAGD;mCACA;yBAKD;qCAK7B;;;;;;AAKA,SAASD,oBAAoBE,KAcnC;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,6BAAkB,CAACL,mBAAmB,EACtC;QACEM,UAAU;IACZ,GACA,IAAMC,4BAA4BL;AAEtC;AAEA,SAASM,0BACPC,QAAgB,EAChBC,UAAkB;IAElB,MAAMC,qBAAqBF,aAAa,MAAM,KAAKA;IACnD,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,gDAAgD,EAAED,mBAAmB,CAAC,EAAED,WAAW,CAAC,CAAC,GADlF,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMG,eAAe;AAErB,eAAeN,4BAA4B,EACzCO,YAAYC,IAAI,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,GAAG,EACHC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EAexB;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,SAAS,EACTC,cAAc,EACZC,eAAe,EACfC,0BAA0B,EAC1BC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,qCAAqC,EACrCC,wCAAwC,EACxCC,kCAAkC,EAClCC,qCAAqC,EACrCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDrC,QAAQ,EACRsC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAG1B;IAEJ,MAAM,EAAE2B,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE,GAC9DC,IAAAA,gCAAe,EAACxC;IAElB,MAAM,EACJyC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACP,aAAaC,QAAQ,EACrBC,SAAS,EACTC,YAAY,EACb,GAAGT;IAEJ,MAAMU,+BAA+B,IAAIC,IAAI9C;IAC7C,MAAM+C,8BAA8B,IAAID,IAAI7C;IAC5C,MAAM+C,2CAA2C,IAAIF,IACnD5C;IAGF,MAAM+C,cAAcC,IAAAA,8BAAc,EAAC;QACjC3C;QACAF;QACA8C,kBAAkBlB;QAClBjC,aAAa6C;QACb5C,YAAY8C;QACZ7C,yBAAyB8C;IAC3B;IAEA,MAAM,CAACI,UAAUC,gBAAgBC,gBAAgB,GAAGf,WAChD,MAAMgB,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUjB,QAAQ,CAAC,EAAE;QACrBkB,cAAclB,QAAQ,CAAC,EAAE;QACzBvC,aAAa6C;QACb5C,YAAY8C;IACd,KACA;QAACW,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGtB,QAChD,MAAMe,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUhB,KAAK,CAAC,EAAE;QAClBiB,cAAcjB,KAAK,CAAC,EAAE;QACtBxC,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAM,CAACgB,SAASC,eAAeC,eAAe,GAAGxB,UAC7C,MAAMc,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUf,OAAO,CAAC,EAAE;QACpBgB,cAAchB,OAAO,CAAC,EAAE;QACxBzC,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAMmB,WAAW,OAAO5B,WAAW;IACnC,MAAM6B,SAAS,OAAOnC,SAAS;IAC/B,MAAM,EAAEoC,KAAKC,eAAe,EAAEC,OAAO,EAAE,GAAG,MAAMrF,IAAAA,iBAAS,IAAGC,KAAK,CAC/DC,6BAAkB,CAACoF,qBAAqB,EACxC;QACEC,UAAU,CAAEN,CAAAA,YAAYC,MAAK;QAC7B/E,UAAU;QACVqF,YAAY;YACV,gBAAgBvC;QAClB;IACF,GACA,IAAMqC,IAAAA,mCAAqB,EAAC1E;IAG9B,MAAM6E,oBAAoB,CAAC,CAACrE,IAAIK,UAAU,CAACiE,OAAO;IAElD;;GAEC,GACD,MAAMC,wBAAwBV,YAAY,CAACnE;IAC3C;;GAEC,GACD,MAAM8E,uCACJ9E,sBAAsB6E;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGrC,WAC/B,MAAMa,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzB1C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAM,CAACiC,WAAWC,gBAAgB,GAChCzE,kBAAkBmC,YACd,MAAMY,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUb,SAAS,CAAC,EAAE;QACtBc,cAAcd,SAAS,CAAC,EAAE;QAC1B3C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAER,MAAM,CAACmC,cAAcC,mBAAmB,GACtC3E,kBAAkBoC,eACd,MAAMW,IAAAA,gEAA+B,EAAC;QACpClD;QACAmD,UAAUZ,YAAY,CAAC,EAAE;QACzBa,cAAcb,YAAY,CAAC,EAAE;QAC7B5C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAER,IAAIqC,UAAUf,mCAAAA,gBAAiBe,OAAO;IAEtC,IAAIzE,qBAAqB,UAAU;QACjC,IAAI,CAACyE,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,gTAAgT,CAAC,GAD9S,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI,OAAOD,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBvE,UAAUyE,kBAAkB,GAAG;QACjC,OAAO,IAAIF,YAAY,iBAAiB;YACtCvE,UAAU0E,YAAY,GAAG;YAEzB,0DAA0D;YAC1D,IAAI1E,UAAU2E,kBAAkB,IAAI,CAAC5E,aAAa6E,iBAAiB,EAAE;gBACnE,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,qBAEX,CAFW,IAAI/D,mBACd,CAAC,qEAAqE,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAd,UAAU8E,uBAAuB,GAAGD,IAAIE,OAAO;gBAC/C/E,UAAUgF,iBAAiB,GAAGH,IAAII,KAAK;gBACvC,MAAMJ;YACR;QACF,OAAO;YACL7E,UAAUyE,kBAAkB,GAAG;YAC/BzE,UAAUkF,WAAW,GAAGX,YAAY;QACtC;IACF;IAEA,IAAI,QAAOf,mCAAAA,gBAAiB2B,UAAU,MAAK,UAAU;QACnDnF,UAAUmF,UAAU,GAAG3B,mCAAAA,gBAAiB2B,UAAU;IACpD;IAEA,IAAI,QAAO3B,mCAAAA,gBAAiB4B,UAAU,MAAK,aAAa;QACtDC,IAAAA,8BAAkB,EAAC7B,mCAAAA,gBAAiB4B,UAAU,EAAEpF,UAAUsF,KAAK;IACjE;IAEA,IAAI,QAAO9B,mCAAAA,gBAAiB4B,UAAU,MAAK,UAAU;QACnD,MAAMG,oBAAoB/B,gBAAgB4B,UAAU;QAEpD,MAAMI,gBAAgBC,kDAAoB,CAACC,QAAQ;QAEnD,IAAIF,eAAe;YACjB,IACEA,cAAcG,IAAI,KAAK,eACvBH,cAAcG,IAAI,KAAK,sBACvBH,cAAcG,IAAI,KAAK,mBACvBH,cAAcG,IAAI,KAAK,SACvB;gBACA,IAAIH,cAAcJ,UAAU,GAAGG,mBAAmB;oBAChDC,cAAcJ,UAAU,GAAGG;gBAC7B;YACF;QACF;QAEA,IACE,CAACvF,UAAUkF,WAAW,IACtBlF,UAAU2E,kBAAkB,IAC5BY,sBAAsB,KACtB,wEAAwE;QACxE,0CAA0C;QAC1C,CAACxF,aAAa6E,iBAAiB,EAC/B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEzD,SAAS;YACrErB,UAAU8E,uBAAuB,GAAGA;YAEpC,MAAM,qBAA+C,CAA/C,IAAIhE,mBAAmBgE,0BAAvB,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;IACF;IAEA,MAAMH,qBAAqB3E,UAAU2E,kBAAkB;IAEvD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0EAA0E;IAC1E,uDAAuD;IACvD,EAAE;IACF,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMiB,4BACJjB,sBAAsB5E,aAAa6E,iBAAiB,KAAK;IAE3D,MAAMiB,eAAqDrC,kBACvDsC,IAAAA,8BAAc,EAACtC,mBACfuC;IAEJ;;GAEC,GACD,IAAIC,iBAAiBH;IAErB,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAC1BC,QAAQ;QACV,IACE,OAAOL,mBAAmB,eAC1B,CAACI,mBAAmBJ,iBACpB;YACAvH,0BAA0BC,UAAU+E,WAAW;QACjD;QAEA,IACE,OAAOV,mBAAmB,eAC1B,CAACqD,mBAAmBrD,iBACpB;YACAtE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOwE,YAAY,eAAe,CAACkD,mBAAmBlD,UAAU;YAClEzE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOuF,aAAa,eAAe,CAACmC,mBAAmBnC,WAAW;YACpExF,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOyF,cAAc,eAAe,CAACiC,mBAAmBjC,YAAY;YACtE1F,0BAA0BC,UAAU;QACtC;QAEA,IACE,OAAO2F,iBAAiB,eACxB,CAAC+B,mBAAmB/B,eACpB;YACA5F,0BAA0BC,UAAU;QACtC;IACF;IAEA,iCAAiC;IACjC,MAAM4H,eAAetF,2BAA2BK;IAEhD,6DAA6D;IAC7D,IAAIkF,gBAAwBtH;IAC5B,IAAIqH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGtH,YAAY;YACf,CAACqH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,4BAA4B;IAC5B,MAAME,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGtF;IAChE,MAAMuF,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB3G,IAAIK,UAAU,CAACgH,sBAAsB;IACvC,MAAMC,MACJb,QAAQC,GAAG,CAACa,YAAY,KAAK,SACzBd,QAAQC,GAAG,CAACc,uBAAuB,GACnCxH,IAAIK,UAAU,CAACiH,GAAG,IAAI;IAE5B,8DAA8D;IAC9D,MAAMG,iBAAiBrH,wCACrB,qBAACA,6CAED,qBAACsH;QAAeC,OAAO5H;;IAGzB,MAAM,CAAC6H,iBAAiBC,iBAAiB,GACvC,MAAMC,gCAAgC;QACpC9H;QACA+H,gBAAgB;QAChBC,WAAWvD;QACXwD,QAAQvD;QACRlF;IACF;IAEF,MAAM,CAAC0I,iBAAiB,GAAG,MAAMJ,gCAAgC;QAC/D9H;QACA+H,gBAAgB;QAChBC,WAAWrD;QACXsD,QAAQrD;QACRpF;IACF;IAEA,MAAM,CAAC2I,oBAAoB,GAAG,MAAML,gCAAgC;QAClE9H;QACA+H,gBAAgB;QAChBC,WAAWnD;QACXoD,QAAQnD;QACRtF;IACF;IAEA,8EAA8E;IAC9E,kBAAkB;IAClB,MAAM4I,mBAAmB,MAAMC,QAAQC,GAAG,CACxCC,OAAOC,IAAI,CAACzG,gBAAgB0G,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,gBAAgB7G,cAAc,CAAC2G,iBAAiB;QAEtD,MAAMG,oBAAoBF,qBACtBf,kBACArB;QAEJ,MAAMuC,qBAAqBH,qBACvBT,mBACA3B;QAEJ,MAAMwC,wBAAwBJ,qBAC1BR,sBACA5B;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIyC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3BvH,cACCiC,CAAAA,WAAW,CAACuF,IAAAA,oDAAyB,EAACL,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAACrI,aAAa6E,iBAAiB,EAC/B;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB1G,cAAc;oBAKxDiJ;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAalH,IAAAA,gCAAe,EAAC4G;gBACnC,KACEM,6BAAAA,WAAWtH,cAAc,qBAAzBsH,2BAA2BC,QAAQ,CAACC,oCAA2B,GAC/D;oBACAnJ,aAAaoJ,GAAG,CAACX;gBACnB;YACF;YAEA,MAAMY,WAAW,MAAMtK,4BAA4B;gBACjDO,YAAYqJ;gBACZnJ,cAAcsH;gBACdrH,oBAAoB8E;gBACpB7E,aAAa6C;gBACb5C,YAAY8C;gBACZ7C,yBAAyB8C;gBACzB,4IAA4I;gBAC5I,8CAA8C;gBAC9C5C,kBAAkB4I,qBACd5I,mBACA,IAAMsI,QAAQkB,OAAO;gBACzBzJ,kBAAkB6I,qBACd7I,mBACA,IAAMuI,QAAQkB,OAAO;gBACzBvJ;gBACAC;gBACAC;gBACAC;gBACA,2HAA2H;gBAC3H,8CAA8C;gBAC9CC,yBAAyBuI,qBACrBvI,0BACA;YACN;YAEA4I,yBAAyBM;QAC3B;QAEA,MAAME,6BACJ,qBAACzG;sBACC,cAAA,qBAAClC;;QAIL,MAAM4I,mBAAmBC,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;QAC5D,MAAMqC,gBAAgBD,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;QACzD,MAAMsC,kBAAkBF,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;QAE3D,MAAMuC,qBACJzC,wBAAwBuC,8BACtB,qBAACjJ;YAAgByF,MAAK;YAAQjH,UAAUyK;sBACrCnG;aAGHA;QAGJ,wFAAwF;QACxF,oCAAoC;QACpC,0BAA0B;QAC1B,MAAMsG,iBAAiBC,oCAAe;QACtC,MAAMC,wBAAwB5C,qCAC5B;;gBACGS,kCACC,qBAACnH;oBACCyF,MAAM,GAAG8D,oCAAe,CAAC,SAAS,CAAC;oBACnC/K,UAAU2I,mBAAmBiC;;gBAGhCF,iCACC,qBAAClJ;oBACCyF,MAAM,GAAG8D,oCAAe,CAAC,OAAO,CAAC;oBACjC/K,UAAU0K,kBAAkBE;;gBAG/BH,+BACC,qBAACjJ;oBACCyF,MAAM,GAAG8D,oCAAe,CAAC,KAAK,CAAC;oBAC/B/K,UAAUyK,gBAAgBG;;;aAK9B;QAEJ,OAAO;YACLpB;0BACA,qBAAC9H;gBACCsJ,mBAAmBxB;gBACnB,sKAAsK;gBACtKvG,OAAOoB;gBACPC,aAAaqG;gBACbpG,cAAcA;gBACdvB,UACE,8DAA8D;gBAC9DkF,wBAAwBqC,iCACtB,qBAAC/I;oBAAgByF,MAAK;oBAAWjH,UAAUuK;8BACxCD;qBAGHA;gBAGJxG,gBAAgBA;gBAChBC,iBAAiBA;gBACjBZ,UAAUwG;gBACVvG,WAAWwG;gBACXvG,cAAcwG;gBACb,GAAI3B,wBAAwB;oBAAE4C;gBAAsB,CAAC;gBAGrD,GAAI3F,qBAAqB;oBAAEA;gBAAkB,CAAC;;YAEjD2E;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAImB,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMxB,iBAAiBR,iBAAkB;QAC5C,MAAM,CAACM,kBAAkB2B,mBAAmBC,WAAW,GAAG1B;QAC1DuB,kBAAkB,CAACzB,iBAAiB,GAAG2B;QACvCD,8BAA8B,CAAC1B,iBAAiB,GAAG4B;IACrD;IAEA,IAAIC,iBAAiB7G,wBAAU,qBAACA,aAAY,OAAS;IACrD,MAAMkG,kBAAkBF,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;IAC3D,IAAIF,wBAAwBmD,gBAAgB;QAC1C,IAAIX,iBAAiB;YACnBW,+BACE,qBAAC7J;gBAECyF,MAAK;gBACLjH,UAAU0K;0BAETW;eAJIjL,eAAe;QAO1B;IACF;IAEA,MAAMkL,cAAiCD,iBACnC;QAACA;QAAgB5G;QAAeC;KAAe,GAC/C;IAEJ,wIAAwI;IACxI,IAAI,CAAC4C,gBAAgB;QACnB,OAAO;YACLU;0BACA,sBAAC7D,cAAK,CAACC,QAAQ;;oBACZV;oBACAuH,mBAAmBM,QAAQ;;eAFTnL;YAIrB8K;YACAI;YACApE;SACD;IACH;IAEA,MAAM4B,YAAYxB;IAClB,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACEhG,UAAU2E,kBAAkB,IAC5B3E,UAAU0E,YAAY,IACtB3E,aAAa6E,iBAAiB,EAC9B;QACA,OAAO;YACL8B;0BACA,sBAAC7D,cAAK,CAACC,QAAQ;;kCACb,qBAAC/B;wBACCmJ,QAAO;wBACP5E,OAAOtF,UAAUsF,KAAK;;oBAEvBlD;;eALkBtD;YAOrB8K;YACAI;YACA;SACD;IACH;IAEA,MAAMG,oBAAoBC,IAAAA,4CAAiB,EAAC5G;IAE5C,IACEyC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAYwD,oBACZ;QACA,6EAA6E;QAC7EU,QAAQ1I,KAAK,CACX,CAAC,uGAAuG,EAAEN,SAAS;IAEvH;IAEA,IAAIiC,QAAQ;QACV,MAAMgH,gBAAgB9C;QAEtB,iDAAiD;QACjD,IAAI+C;QACJ,IAAIJ,mBAAmB;YACrB,IAAIxF,oBAAoB;gBACtB,MAAM6F,kBAAkB5J,sCACtB2F,eACAvG;gBAEF,MAAMyK,wBACJ/J,yCAAyCV;gBAC3CuK,4BACE,qBAAChK;oBACCiH,WAAW8C;oBACXI,cAAcxJ;oBACdyJ,QAAQpE;oBACRqE,UAAU;wBAACH;wBAAuBD;qBAAgB;;YAGxD,OAAO;gBACLD,4BACE,qBAAChK;oBACCiH,WAAW8C;oBACXI,cAAcxJ;oBACdyJ,QAAQpE;;YAGd;QACF,OAAO;YACL,uEAAuE;YACvE,wEAAwE;YACxE,MAAMoE,SAAShK,mCACb4F,eACAvG;YAGF,uEAAuE;YACvE,uEAAuE;YACvE,SAAS;YACT,IAAI0K,eAAejK,sCAAsCS,OAAOlB;YAEhE,IAAI6K,IAAAA,6CAAkB,EAACP,gBAAgB;gBACrC,MAAMQ,wBACJR;gBAEF,IAAI,CAACvK,aAAagL,SAAS,EAAE;oBAC3B,gEAAgE;oBAChE,iEAAiE;oBACjE,YAAY;oBACZL,eAAe7C,QAAQkB,OAAO,CAAC,CAAC;gBAClC;gBAEAwB,4BACE,qBAACO;oBACCH,QAAQA;oBACRD,cAAcA;oBACdM,iBAAiB;;YAGvB,OAAO;gBACLT,4BACE,qBAACD;oBAAcK,QAAQA;oBAAQD,cAAcA;;YAEjD;QACF;QAEA,MAAMO,mBAAmB5J,YAAY6J,4BAAmB;QACxD,MAAMC,eACJjC,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK,WACnCoC,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;QACrC,MAAMsE,cAAcH,mBAAmB,YAAY;QACnD,MAAMI,qBACJzE,wBAAwBuE,6BACtB,qBAACjL;YAECyF,MAAMyF;YACN1M,UAAUyM;sBAETZ;WAJIzL,eAAe,MAAMsM,eAO5Bb;QAGJ,OAAO;YACL7D;0BACA,sBAAC7D,cAAK,CAACC,QAAQ;;oBACZuI;oBACAjJ;kCACD,sBAAC9B;;0CACC,qBAAC4G;gCAAeC,OAAO7H;;4BACtB2H;;;;eALgBnI;YAQrB8K;YACAI;YACApE;SACD;IACH,OAAO;QACL,MAAM0F,mBAAmB9D;QACzB,MAAM+D,oDACJxH,yBACA,cAAcxC,kBACdwG,OAAOC,IAAI,CAACzG,gBAAgBiK,MAAM,GAAG;QAEvC,IAAIC;QAEJ,IAAItB,mBAAmB;YACrB,IAAIuB;YAEJ,IAAI/G,oBAAoB;gBACtB,MAAM6F,kBAAkB5J,sCACtB2F,eACAvG;gBAGF0L,8BACE,qBAAClL;oBACCgH,WAAW8D;oBACXK,OAAOhC;oBACPgB,QAAQpE;oBACRqF,SAASpB;;YAGf,OAAO;gBACLkB,8BACE,qBAAClL;oBACCgH,WAAW8D;oBACXK,OAAOhC;oBACPgB,QAAQpE;;YAGd;YAEA,IAAIgF,mDAAmD;gBACrD,IAAIM;gBACJ,IAAIC;gBACJ,IAAIC;gBACJ,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCF,wBAAwBG,qCAAqC;oBAC3DC,wBAAwBhI;oBACxBiI,cAAc9E;oBACd5G;oBACA4B;oBACAkJ;oBACA/E;gBACF;gBACAuF,yBAAyBE,qCAAqC;oBAC5DC,wBAAwB9H;oBACxB+H,cAAcxE;oBACdlH;oBACA4B;oBACAkJ;oBACA/E;gBACF;gBACAwF,4BAA4BC,qCAAqC;oBAC/DC,wBAAwB5H;oBACxB6H,cAAcvE;oBACdnH;oBACA4B;oBACAkJ;oBACA/E;gBACF;gBACA,IACEsF,yBACAC,0BACAC,2BACA;oBACAN,4BACE,sBAACtL;wBAEC0B,UAAUgK;wBACV/J,WAAWgK;wBACX/J,cAAcgK;;4BAEb3J;4BACAsJ;;uBANI5M;gBASX,OAAO;oBACL2M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;4BACZV;4BACAsJ;;uBAFkB5M;gBAKzB;YACF,OAAO;gBACL2M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;wBACZV;wBACAsJ;;mBAFkB5M;YAKzB;QACF,OAAO;YACL,MAAM6L,SAAShK,mCACb4F,eACAvG;YAGF,IAAImM;YAEJ,IAAItB,IAAAA,6CAAkB,EAACS,mBAAmB;gBACxC,MAAMc,0BACJd;gBAEFa,8BACE,qBAACC;oBACE,GAAGzC,kBAAkB;oBACtBgB,QAAQA;oBACR0B,mBAAmB;;YAGzB,OAAO;gBACLF,8BACE,qBAACb;oBAAkB,GAAG3B,kBAAkB;oBAAEgB,QAAQA;;YAEtD;YAEA,IAAIY,mDAAmD;gBACrD,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCE,4BACE,sBAACtL;oBAEC0B,UACEuF,gCACE;;4BACGhF;0CACD,sBAACkJ;gCAAiBX,QAAQA;;oCACvBzG;oCACAkD;;;;yBAGHrB;;wBAGL3D;wBACA+J;;mBAdIrN;YAiBX,OAAO;gBACL2M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;wBACZV;wBACA+J;;mBAFkBrN;YAKzB;QACF;QAEA,MAAMwN,iBAAiBpD,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAK;QAC1D,MAAMyF,qBACJ3F,wBAAwB0F,+BACtB,qBAACpM;YAA6ByF,MAAK;YAASjH,UAAU4N;sBACnDb;WADkB,YAIrBA;QAGJ,2CAA2C;QAC3C,OAAO;YACL/E;YACA6F;YACA3C;YACAI;YACApE;SACD;IACH;AACF;AAEA,eAAesB,eAAe,EAC5BC,KAAK,EAGN;IACC,MAAMqF,IAAIrF;IACV,+FAA+F;IAC/F,IAAIqF,EAAEC,MAAM,KAAK,YAAY;QAC3B,MAAMD,EAAEhG,KAAK;IACf,OAAO,IAAIgG,EAAEC,MAAM,KAAK,aAAa;QACnC,MAAMD;IACR;IACA,OAAO;AACT;AACAtF,eAAewF,WAAW,GAAGC,uCAAoB;AAEjD,SAASX,qCAAqC,EAC5CC,sBAAsB,EACtBC,YAAY,EACZ1L,iBAAiB,EACjB4B,WAAW,EACXkJ,gBAAgB,EAChB/E,aAAa,EAQd;IACC,IAAI0F,wBAAwB;QAC1B,MAAMW,6BAA6B;YACjC3C,UAAUiC;QACZ;QACA,qBACE;;gBACG9J;8BACD,qBAAC5B;oBACCgH,WAAW8D;oBACXK,OAAOiB;oBACPjC,QAAQpE;;;;IAIhB;IACA,OAAO;AACT;AAEO,SAASrI,cACda,UAAsB,EACtBiC,0BAAsD;IAEtD,OAAO6L,kBAAkB,CAAC,GAAG9N,YAAYiC;AAC3C;AAEA,SAAS6L,kBACP5N,YAAoB,EACpBF,UAAsB,EACtBiC,0BAAsD;IAEtD,MAAM,EACJK,OAAO,EACPC,SAAS,EAAEG,MAAM,EAAE,EACnBF,cAAc,EACf,GAAGC,IAAAA,gCAAe,EAACzC;IAEpB,MAAMuH,eAAetF,2BAA2BK;IAEhD,IAAIkF,gBAAwBtH;IAC5B,IAAIqH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGtH,YAAY;YACf,CAACqH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAMsG,eAAe,OAAOrL,WAAW;IAEvC,IAAIqL,cAAc;QAChB,OAAOvG;IACT,OAAO,IAAI,CAAChF,eAAe0I,QAAQ,EAAE;QACnC,4EAA4E;QAC5E,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,wFAAwF;QACxF,OAAO1D;IACT,OAAO;QACL,OAAOsG,kBACLtG,eACA,qEAAqE;QACrE,8EAA8E;QAC9E,+EAA+E;QAC/E,+CAA+C;QAC/ChF,eAAe0I,QAAQ,EACvBjJ;IAEJ;AACF;AAEA,eAAesG,gCAAgC,EAC7C9H,GAAG,EACH+H,cAAc,EACdC,SAAS,EACTC,MAAM,EACNzI,IAAI,EAYL;IACC,MAAM4H,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB3G,IAAIK,UAAU,CAACgH,sBAAsB;IACvC,MAAMC,MACJb,QAAQC,GAAG,CAACa,YAAY,KAAK,SACzBd,QAAQC,GAAG,CAACc,uBAAuB,GACnCxH,IAAIK,UAAU,CAACiH,GAAG,IAAI;IAC5B,MAAM,EAAE5G,eAAe,EAAE,GAAGV,IAAIS,YAAY;IAC5C,MAAM8M,UAAUvF,0BACd;;0BACE,qBAACA;YACAC;;SAED1B;IAEJ,MAAMrH,WAAWwK,IAAAA,4CAAuB,EAAClK,MAAM8H,KAAKS;IAEpD,MAAMyF,iBACJpG,wBAAwBmG,wBACtB,qBAAC7M;QAECyF,MAAM4B;QACN7I,UAAUA;kBAETqO;OAJIjO,eAAe,MAAMyI,kBAO5BwF;IAGJ,OAAO;QAACC;QAAgBtO;KAAS;AACnC", "ignoreList": [0]}