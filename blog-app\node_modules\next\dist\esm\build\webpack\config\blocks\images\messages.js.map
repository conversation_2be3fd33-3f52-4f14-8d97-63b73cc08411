{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/images/messages.ts"], "sourcesContent": ["import { bold, cyan } from '../../../../../lib/picocolors'\n\nexport function getCustomDocumentImageError() {\n  return `Images ${bold('cannot')} be imported within ${cyan(\n    'pages/_document.js'\n  )}. Please move image imports that need to be displayed on every page into ${cyan(\n    'pages/_app.js'\n  )}.\\nRead more: https://nextjs.org/docs/messages/custom-document-image-import`\n}\n"], "names": ["bold", "cyan", "getCustomDocumentImageError"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,gCAA+B;AAE1D,OAAO,SAASC;IACd,OAAO,CAAC,OAAO,EAAEF,KAAK,UAAU,oBAAoB,EAAEC,KACpD,sBACA,yEAAyE,EAAEA,KAC3E,iBACA,2EAA2E,CAAC;AAChF", "ignoreList": [0]}