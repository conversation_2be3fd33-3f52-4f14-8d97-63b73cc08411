import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";import{getDotPath as t}from"@standard-schema/utils";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},n.apply(null,arguments)}function s(s,a,i){return void 0===i&&(i={}),function(a,o,u){try{var l=function(){if(c.issues){var s=function(e,r){for(var s={},a=0;a<e.length;a++){var i=e[a],o=t(i);if(o&&(s[o]||(s[o]={message:i.message,type:""}),r)){var u,l=s[o].types||{};s[o].types=n({},l,((u={})[Object.keys(l).length]=i.message,u))}}return s}(c.issues,!u.shouldUseNativeValidation&&"all"===u.criteriaMode);return{values:{},errors:e(s,u)}}return u.shouldUseNativeValidation&&r({},u),{values:i.raw?Object.assign({},a):c.value,errors:{}}},c=s["~standard"].validate(a),f=function(){if(c instanceof Promise)return Promise.resolve(c).then(function(e){c=e})}();return Promise.resolve(f&&f.then?f.then(l):l())}catch(e){return Promise.reject(e)}}}export{s as standardSchemaResolver};
//# sourceMappingURL=standard-schema.module.js.map
