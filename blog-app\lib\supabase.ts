import { createClient } from '@supabase/supabase-js'

// 环境变量检查
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'

// 简化的客户端实例
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 客户端 Supabase 实例（用于客户端组件）
export const createClientComponentClient = () => {
  return supabase
}




