{"version": 3, "sources": ["../../src/lib/find-pages-dir.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\n\nexport function findDir(dir: string, name: 'pages' | 'app'): string | null {\n  // prioritize ./${name} over ./src/${name}\n  let curDir = path.join(dir, name)\n  if (fs.existsSync(curDir)) return curDir\n\n  curDir = path.join(dir, 'src', name)\n  if (fs.existsSync(curDir)) return curDir\n\n  return null\n}\n\nexport function findPagesDir(dir: string): {\n  pagesDir: string | undefined\n  appDir: string | undefined\n} {\n  const pagesDir = findDir(dir, 'pages') || undefined\n  const appDir = findDir(dir, 'app') || undefined\n\n  if (appDir == null && pagesDir == null) {\n    throw new Error(\n      \"> Couldn't find any `pages` or `app` directory. Please create one under the project root\"\n    )\n  }\n\n  return {\n    pagesDir,\n    appDir,\n  }\n}\n"], "names": ["fs", "path", "findDir", "dir", "name", "curDir", "join", "existsSync", "findPagesDir", "pagesDir", "undefined", "appDir", "Error"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AAEvB,OAAO,SAASC,QAAQC,GAAW,EAAEC,IAAqB;IACxD,0CAA0C;IAC1C,IAAIC,SAASJ,KAAKK,IAAI,CAACH,KAAKC;IAC5B,IAAIJ,GAAGO,UAAU,CAACF,SAAS,OAAOA;IAElCA,SAASJ,KAAKK,IAAI,CAACH,KAAK,OAAOC;IAC/B,IAAIJ,GAAGO,UAAU,CAACF,SAAS,OAAOA;IAElC,OAAO;AACT;AAEA,OAAO,SAASG,aAAaL,GAAW;IAItC,MAAMM,WAAWP,QAAQC,KAAK,YAAYO;IAC1C,MAAMC,SAAST,QAAQC,KAAK,UAAUO;IAEtC,IAAIC,UAAU,QAAQF,YAAY,MAAM;QACtC,MAAM,qBAEL,CAFK,IAAIG,MACR,6FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO;QACLH;QACAE;IACF;AACF", "ignoreList": [0]}