{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n          博客应用\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          欢迎来到我们的博客平台\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAAwD;;;;;;8BAGtE,6WAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}]}