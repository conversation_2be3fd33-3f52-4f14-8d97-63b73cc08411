'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, label, error, helperText, leftIcon, rightIcon, type = 'text', ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false)
    const [hasValue, setHasValue] = React.useState(false)

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0)
      props.onChange?.(e)
    }

    const inputClasses = cn(
      // 基础样式
      'w-full rounded-xl border-2 bg-white dark:bg-gray-900 transition-all duration-200',
      'text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400',
      'focus:outline-none focus:ring-0',
      
      // 内边距
      {
        'pl-12 pr-4 py-3': leftIcon && !rightIcon,
        'pl-4 pr-12 py-3': rightIcon && !leftIcon,
        'pl-12 pr-12 py-3': leftIcon && rightIcon,
        'px-4 py-3': !leftIcon && !rightIcon,
      },
      
      // 状态样式
      {
        'border-gray-200 dark:border-gray-700': !error && !isFocused,
        'border-apple-blue shadow-apple': !error && isFocused,
        'border-apple-red': error,
      },
      
      className
    )

    return (
      <div className="w-full">
        {label && (
          <motion.label
            className={cn(
              'block text-sm font-medium mb-2 transition-colors duration-200',
              error ? 'text-apple-red' : 'text-gray-700 dark:text-gray-300'
            )}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {label}
          </motion.label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              {leftIcon}
            </div>
          )}
          
          <motion.input
            ref={ref}
            type={type}
            className={inputClasses}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onChange={handleChange}
            whileFocus={{ scale: 1.01 }}
            {...(props as any)}
          />
          
          {rightIcon && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <motion.p
            className={cn(
              'mt-2 text-sm',
              error ? 'text-apple-red' : 'text-gray-500 dark:text-gray-400'
            )}
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {error || helperText}
          </motion.p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input }
