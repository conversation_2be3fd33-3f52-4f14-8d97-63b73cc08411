@import "tailwindcss";

/* 苹果风格全局样式 */
:root {
  --background: #ffffff;
  --foreground: #1d1d1f;
  --background-secondary: #f5f5f7;
  --border-color: #d2d2d7;
  --text-secondary: #86868b;

  /* 苹果风格阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 32px 0 rgba(0, 0, 0, 0.12);

  /* 毛玻璃效果 */
  --backdrop-blur: blur(20px);
  --backdrop-saturate: saturate(180%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #f5f5f7;
    --background-secondary: #1d1d1f;
    --border-color: #424245;
    --text-secondary: #86868b;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  font-size: 16px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 苹果风格滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* 苹果风格选择文本 */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: inherit;
}

/* 苹果风格焦点样式 */
:focus-visible {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
  border-radius: 4px;
}

/* 苹果风格按钮基础样式 */
button {
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

button:active {
  transform: scale(0.98);
}

/* 苹果风格输入框基础样式 */
input, textarea {
  font-family: inherit;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 苹果风格链接 */
a {
  color: #007AFF;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #0056CC;
}

/* 毛玻璃效果工具类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
  -webkit-backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* 苹果风格卡片 */
.apple-card {
  background: var(--background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.apple-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}
