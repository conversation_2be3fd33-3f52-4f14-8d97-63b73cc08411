{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,gBAAgB", "ignoreList": [0]}