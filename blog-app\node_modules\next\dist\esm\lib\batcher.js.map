{"version": 3, "sources": ["../../src/lib/batcher.ts"], "sourcesContent": ["import type { SchedulerFn } from './scheduler'\n\nimport { DetachedPromise } from './detached-promise'\n\ntype CacheKeyFn<K, C extends string | number | null> = (\n  key: K\n) => PromiseLike<C> | C\n\ntype BatcherOptions<K, C extends string | number | null> = {\n  cacheKeyFn?: CacheKeyFn<K, C>\n  schedulerFn?: SchedulerFn<void>\n}\n\ntype WorkFn<V, C> = (\n  key: C,\n  resolve: (value: V | PromiseLike<V>) => void\n) => Promise<V>\n\n/**\n * A wrapper for a function that will only allow one call to the function to\n * execute at a time.\n */\nexport class Batcher<K, V, C extends string | number | null> {\n  private readonly pending = new Map<C, Promise<V>>()\n\n  protected constructor(\n    private readonly cacheKeyFn?: CacheKeyFn<K, C>,\n    /**\n     * A function that will be called to schedule the wrapped function to be\n     * executed. This defaults to a function that will execute the function\n     * immediately.\n     */\n    private readonly schedulerFn: SchedulerFn<void> = (fn) => fn()\n  ) {}\n\n  /**\n   * Creates a new instance of PendingWrapper. If the key extends a string or\n   * number, the key will be used as the cache key. If the key is an object, a\n   * cache key function must be provided.\n   */\n  public static create<K extends string | number | null, V>(\n    options?: BatcherOptions<K, K>\n  ): Batcher<K, V, K>\n  public static create<K, V, C extends string | number | null>(\n    options: BatcherOptions<K, C> &\n      Required<Pick<BatcherOptions<K, C>, 'cacheKeyFn'>>\n  ): Batcher<K, V, C>\n  public static create<K, V, C extends string | number | null>(\n    options?: BatcherOptions<K, C>\n  ): Batcher<K, V, C> {\n    return new Batcher<K, V, C>(options?.cacheKeyFn, options?.schedulerFn)\n  }\n\n  /**\n   * Wraps a function in a promise that will be resolved or rejected only once\n   * for a given key. This will allow multiple calls to the function to be\n   * made, but only one will be executed at a time. The result of the first\n   * call will be returned to all callers.\n   *\n   * @param key the key to use for the cache\n   * @param fn the function to wrap\n   * @returns a promise that resolves to the result of the function\n   */\n  public async batch(key: K, fn: WorkFn<V, C>): Promise<V> {\n    const cacheKey = (this.cacheKeyFn ? await this.cacheKeyFn(key) : key) as C\n    if (cacheKey === null) {\n      return fn(cacheKey, Promise.resolve)\n    }\n\n    const pending = this.pending.get(cacheKey)\n    if (pending) return pending\n\n    const { promise, resolve, reject } = new DetachedPromise<V>()\n    this.pending.set(cacheKey, promise)\n\n    this.schedulerFn(async () => {\n      try {\n        const result = await fn(cacheKey, resolve)\n\n        // Resolving a promise multiple times is a no-op, so we can safely\n        // resolve all pending promises with the same result.\n        resolve(result)\n      } catch (err) {\n        reject(err)\n      } finally {\n        this.pending.delete(cacheKey)\n      }\n    })\n\n    return promise\n  }\n}\n"], "names": ["Detached<PERSON>romise", "<PERSON><PERSON>", "cacheKeyFn", "schedulerFn", "fn", "pending", "Map", "create", "options", "batch", "key", "cache<PERSON>ey", "Promise", "resolve", "get", "promise", "reject", "set", "result", "err", "delete"], "mappings": "AAEA,SAASA,eAAe,QAAQ,qBAAoB;AAgBpD;;;CAGC,GACD,OAAO,MAAMC;IAGX,YACE,AAAiBC,UAA6B,EAC9C;;;;KAIC,GACD,AAAiBC,cAAiC,CAACC,KAAOA,IAAI,CAC9D;aAPiBF,aAAAA;aAMAC,cAAAA;aATFE,UAAU,IAAIC;IAU5B;IAcH,OAAcC,OACZC,OAA8B,EACZ;QAClB,OAAO,IAAIP,QAAiBO,2BAAAA,QAASN,UAAU,EAAEM,2BAAAA,QAASL,WAAW;IACvE;IAEA;;;;;;;;;GASC,GACD,MAAaM,MAAMC,GAAM,EAAEN,EAAgB,EAAc;QACvD,MAAMO,WAAY,IAAI,CAACT,UAAU,GAAG,MAAM,IAAI,CAACA,UAAU,CAACQ,OAAOA;QACjE,IAAIC,aAAa,MAAM;YACrB,OAAOP,GAAGO,UAAUC,QAAQC,OAAO;QACrC;QAEA,MAAMR,UAAU,IAAI,CAACA,OAAO,CAACS,GAAG,CAACH;QACjC,IAAIN,SAAS,OAAOA;QAEpB,MAAM,EAAEU,OAAO,EAAEF,OAAO,EAAEG,MAAM,EAAE,GAAG,IAAIhB;QACzC,IAAI,CAACK,OAAO,CAACY,GAAG,CAACN,UAAUI;QAE3B,IAAI,CAACZ,WAAW,CAAC;YACf,IAAI;gBACF,MAAMe,SAAS,MAAMd,GAAGO,UAAUE;gBAElC,kEAAkE;gBAClE,qDAAqD;gBACrDA,QAAQK;YACV,EAAE,OAAOC,KAAK;gBACZH,OAAOG;YACT,SAAU;gBACR,IAAI,CAACd,OAAO,CAACe,MAAM,CAACT;YACtB;QACF;QAEA,OAAOI;IACT;AACF", "ignoreList": [0]}