{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-bundle-path-normalizer.ts"], "sourcesContent": ["import { Normalizers } from '../../normalizers'\nimport type { Normalizer } from '../../normalizer'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { UnderscoreNormalizer } from '../../underscore-normalizer'\n\nexport class AppBundlePathNormalizer extends PrefixingNormalizer {\n  constructor() {\n    super('app')\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(normalizePagePath(page))\n  }\n}\n\nexport class DevAppBundlePathNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer, isTurbopack: boolean) {\n    const normalizers = [\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppBundlePathNormalizer(),\n    ]\n\n    // %5F to _ replacement should only happen with Turbopack.\n    if (isTurbopack) {\n      normalizers.unshift(new UnderscoreNormalizer())\n    }\n    super(normalizers)\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["Normalizers", "PrefixingNormalizer", "normalizePagePath", "UnderscoreNormalizer", "AppBundlePathNormalizer", "constructor", "normalize", "page", "DevAppBundlePathNormalizer", "pageNormalizer", "isTurbopack", "normalizers", "unshift", "filename"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAmB;AAE/C,SAASC,mBAAmB,QAAQ,6BAA4B;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,oBAAoB,QAAQ,8BAA6B;AAElE,OAAO,MAAMC,gCAAgCH;IAC3CI,aAAc;QACZ,KAAK,CAAC;IACR;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUJ,kBAAkBK;IAC3C;AACF;AAEA,OAAO,MAAMC,mCAAmCR;IAC9CK,YAAYI,cAA0B,EAAEC,WAAoB,CAAE;QAC5D,MAAMC,cAAc;YAClB,gDAAgD;YAChDF;YACA,wCAAwC;YACxC,IAAIL;SACL;QAED,0DAA0D;QAC1D,IAAIM,aAAa;YACfC,YAAYC,OAAO,CAAC,IAAIT;QAC1B;QACA,KAAK,CAACQ;IACR;IAEOL,UAAUO,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACP,UAAUO;IACzB;AACF", "ignoreList": [0]}