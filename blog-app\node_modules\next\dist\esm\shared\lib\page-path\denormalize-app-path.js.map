{"version": 3, "sources": ["../../../../src/shared/lib/page-path/denormalize-app-path.ts"], "sourcesContent": ["export function denormalizeAppPagePath(page: string): string {\n  // `/` is normalized to `/index`\n  if (page === '/index') {\n    return '/'\n  }\n\n  return page\n}\n"], "names": ["denormalizeAppPagePath", "page"], "mappings": "AAAA,OAAO,SAASA,uBAAuBC,IAAY;IACjD,gCAAgC;IAChC,IAAIA,SAAS,UAAU;QACrB,OAAO;IACT;IAEA,OAAOA;AACT", "ignoreList": [0]}