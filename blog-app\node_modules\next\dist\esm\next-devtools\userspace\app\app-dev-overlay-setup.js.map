{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/app-dev-overlay-setup.ts"], "sourcesContent": ["import { patchConsoleError } from './errors/intercept-console-error'\nimport { handleGlobalErrors } from './errors/use-error-handler'\nimport {\n  initializeDebugLogForwarding,\n  isTerminalLoggingEnabled,\n} from './forward-logs'\n\nhandleGlobalErrors()\npatchConsoleError()\n\nif (isTerminalLoggingEnabled) {\n  initializeDebugLogForwarding('app')\n}\n"], "names": ["patchConsoleError", "handleGlobalErrors", "initializeDebugLogForwarding", "isTerminalLoggingEnabled"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mCAAkC;AACpE,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SACEC,4BAA4B,EAC5BC,wBAAwB,QACnB,iBAAgB;AAEvBF;AACAD;AAEA,IAAIG,0BAA0B;IAC5BD,6BAA6B;AAC/B", "ignoreList": [0]}