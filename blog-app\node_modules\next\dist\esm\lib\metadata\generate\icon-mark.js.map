{"version": 3, "sources": ["../../../../src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": "AAAA;;AAEA,2DAA2D;AAC3D,2EAA2E;AAC3E,mFAAmF;AACnF,0FAA0F;AAC1F,oGAAoG;AACpG,kGAAkG;AAClG,OAAO,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IACA,qBAAO,KAACC;QAAKC,MAAK;;AACpB,EAAC", "ignoreList": [0]}