{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'node:http'\nimport type { SizeLimit } from '../../types'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { AppRenderContext, GenerateFlight } from './app-render'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\n\nimport {\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  ACTION_HEADER,\n  NEXT_ACTION_NOT_FOUND_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n} from '../../client/components/app-router-headers'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getRedirectTypeFromError,\n  getURLFromRedirectError,\n} from '../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectType,\n} from '../../client/components/redirect-error'\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n} from '../render-result'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  filterReqHeaders,\n  actionsForbiddenHeaders,\n} from '../lib/server-ipc/utils'\nimport { getModifiedCookieValues } from '../web/spec-extension/adapters/request-cookies'\n\nimport {\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n} from '../../lib/constants'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { isCsrfOriginAllowed } from './csrf-protection'\nimport { warn } from '../../build/output/log'\nimport { RequestCookies, ResponseCookies } from '../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport { fromNodeOutgoingHttpHeaders } from '../web/utils'\nimport { selectWorkerForForwarding } from './action-utils'\nimport { isNodeNextRequest, isWebNextRequest } from '../base-http/helpers'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { synchronizeMutableCookies } from '../async-storage/request-store'\nimport type { TemporaryReferenceSet } from 'react-server-dom-webpack/server'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { executeRevalidates } from '../revalidation-utils'\nimport { getRequestMeta } from '../request-meta'\nimport { setCacheBustingSearchParam } from '../../client/components/router-reducer/set-cache-busting-search-param'\n\nfunction formDataFromSearchQueryString(query: string) {\n  const searchParams = new URLSearchParams(query)\n  const formData = new FormData()\n  for (const [key, value] of searchParams) {\n    formData.append(key, value)\n  }\n  return formData\n}\n\nfunction nodeHeadersToRecord(\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders\n) {\n  const record: Record<string, string> = {}\n  for (const [key, value] of Object.entries(headers)) {\n    if (value !== undefined) {\n      record[key] = Array.isArray(value) ? value.join(', ') : `${value}`\n    }\n  }\n  return record\n}\n\nfunction getForwardedHeaders(\n  req: BaseNextRequest,\n  res: BaseNextResponse\n): Headers {\n  // Get request headers and cookies\n  const requestHeaders = req.headers\n  const requestCookies = new RequestCookies(HeadersAdapter.from(requestHeaders))\n\n  // Get response headers and cookies\n  const responseHeaders = res.getHeaders()\n  const responseCookies = new ResponseCookies(\n    fromNodeOutgoingHttpHeaders(responseHeaders)\n  )\n\n  // Merge request and response headers\n  const mergedHeaders = filterReqHeaders(\n    {\n      ...nodeHeadersToRecord(requestHeaders),\n      ...nodeHeadersToRecord(responseHeaders),\n    },\n    actionsForbiddenHeaders\n  ) as Record<string, string>\n\n  // Merge cookies into requestCookies, so responseCookies always take precedence\n  // and overwrite/delete those from requestCookies.\n  responseCookies.getAll().forEach((cookie) => {\n    if (typeof cookie.value === 'undefined') {\n      requestCookies.delete(cookie.name)\n    } else {\n      requestCookies.set(cookie)\n    }\n  })\n\n  // Update the 'cookie' header with the merged cookies\n  mergedHeaders['cookie'] = requestCookies.toString()\n\n  // Remove headers that should not be forwarded\n  delete mergedHeaders['transfer-encoding']\n\n  return new Headers(mergedHeaders)\n}\n\nfunction addRevalidationHeader(\n  res: BaseNextResponse,\n  {\n    workStore,\n    requestStore,\n  }: {\n    workStore: WorkStore\n    requestStore: RequestStore\n  }\n) {\n  // If a tag was revalidated, the client router needs to invalidate all the\n  // client router cache as they may be stale. And if a path was revalidated, the\n  // client needs to invalidate all subtrees below that path.\n\n  // To keep the header size small, we use a tuple of\n  // [[revalidatedPaths], isTagRevalidated ? 1 : 0, isCookieRevalidated ? 1 : 0]\n  // instead of a JSON object.\n\n  // TODO-APP: Currently the prefetch cache doesn't have subtree information,\n  // so we need to invalidate the entire cache if a path was revalidated.\n  // TODO-APP: Currently paths are treated as tags, so the second element of the tuple\n  // is always empty.\n\n  const isTagRevalidated = workStore.pendingRevalidatedTags?.length ? 1 : 0\n  const isCookieRevalidated = getModifiedCookieValues(\n    requestStore.mutableCookies\n  ).length\n    ? 1\n    : 0\n\n  res.setHeader(\n    'x-action-revalidated',\n    JSON.stringify([[], isTagRevalidated, isCookieRevalidated])\n  )\n}\n\n/**\n * Forwards a server action request to a separate worker. Used when the requested action is not available in the current worker.\n */\nasync function createForwardedActionResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  host: Host,\n  workerPathname: string,\n  basePath: string\n) {\n  if (!host) {\n    throw new Error(\n      'Invariant: Missing `host` header from a forwarded Server Actions request.'\n    )\n  }\n\n  const forwardedHeaders = getForwardedHeaders(req, res)\n\n  // indicate that this action request was forwarded from another worker\n  // we use this to skip rendering the flight tree so that we don't update the UI\n  // with the response from the forwarded worker\n  forwardedHeaders.set('x-action-forwarded', '1')\n\n  const proto =\n    getRequestMeta(req, 'initProtocol')?.replace(/:+$/, '') || 'https'\n\n  // For standalone or the serverful mode, use the internal origin directly\n  // other than the host headers from the request.\n  const origin = process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${host.value}`\n\n  const fetchUrl = new URL(`${origin}${basePath}${workerPathname}`)\n\n  try {\n    let body: BodyInit | ReadableStream<Uint8Array> | undefined\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(req)\n    ) {\n      if (!req.body) {\n        throw new Error('Invariant: missing request body.')\n      }\n\n      body = req.body\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      body = req.stream()\n    } else {\n      throw new Error('Invariant: Unknown request type.')\n    }\n\n    // Forward the request to the new worker\n    const response = await fetch(fetchUrl, {\n      method: 'POST',\n      body,\n      duplex: 'half',\n      headers: forwardedHeaders,\n      redirect: 'manual',\n      next: {\n        // @ts-ignore\n        internal: 1,\n      },\n    })\n\n    if (\n      response.headers.get('content-type')?.startsWith(RSC_CONTENT_TYPE_HEADER)\n    ) {\n      // copy the headers from the redirect response to the response we're sending\n      for (const [key, value] of response.headers) {\n        if (!actionsForbiddenHeaders.includes(key)) {\n          res.setHeader(key, value)\n        }\n      }\n\n      return new FlightRenderResult(response.body!)\n    } else {\n      // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n      response.body?.cancel()\n    }\n  } catch (err) {\n    // we couldn't stream the forwarded response, so we'll just return an empty response\n    console.error(`failed to forward action response`, err)\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n/**\n * Returns the parsed redirect URL if we deem that it is hosted by us.\n *\n * We handle both relative and absolute redirect URLs.\n *\n * In case the redirect URL is not relative to the application we return `null`.\n */\nfunction getAppRelativeRedirectUrl(\n  basePath: string,\n  host: Host,\n  redirectUrl: string\n): URL | null {\n  if (redirectUrl.startsWith('/') || redirectUrl.startsWith('.')) {\n    // Make sure we are appending the basePath to relative URLS\n    return new URL(`${basePath}${redirectUrl}`, 'http://n')\n  }\n\n  const parsedRedirectUrl = new URL(redirectUrl)\n\n  if (host?.value !== parsedRedirectUrl.host) {\n    return null\n  }\n\n  // At this point the hosts are the same, just confirm we\n  // are routing to a path underneath the `basePath`\n  return parsedRedirectUrl.pathname.startsWith(basePath)\n    ? parsedRedirectUrl\n    : null\n}\n\nasync function createRedirectRenderResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  originalHost: Host,\n  redirectUrl: string,\n  redirectType: RedirectType,\n  basePath: string,\n  workStore: WorkStore\n) {\n  res.setHeader('x-action-redirect', `${redirectUrl};${redirectType}`)\n\n  // If we're redirecting to another route of this Next.js application, we'll\n  // try to stream the response from the other worker path. When that works,\n  // we can save an extra roundtrip and avoid a full page reload.\n  // When the redirect URL starts with a `/` or is to the same host, under the\n  // `basePath` we treat it as an app-relative redirect;\n  const appRelativeRedirectUrl = getAppRelativeRedirectUrl(\n    basePath,\n    originalHost,\n    redirectUrl\n  )\n\n  if (appRelativeRedirectUrl) {\n    if (!originalHost) {\n      throw new Error(\n        'Invariant: Missing `host` header from a forwarded Server Actions request.'\n      )\n    }\n\n    const forwardedHeaders = getForwardedHeaders(req, res)\n    forwardedHeaders.set(RSC_HEADER, '1')\n\n    const proto =\n      getRequestMeta(req, 'initProtocol')?.replace(/:+$/, '') || 'https'\n\n    // For standalone or the serverful mode, use the internal origin directly\n    // other than the host headers from the request.\n    const origin =\n      process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${originalHost.value}`\n\n    const fetchUrl = new URL(\n      `${origin}${appRelativeRedirectUrl.pathname}${appRelativeRedirectUrl.search}`\n    )\n\n    if (workStore.pendingRevalidatedTags) {\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n        workStore.pendingRevalidatedTags.join(',')\n      )\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n        workStore.incrementalCache?.prerenderManifest?.preview?.previewModeId ||\n          ''\n      )\n    }\n\n    // Ensures that when the path was revalidated we don't return a partial response on redirects\n    forwardedHeaders.delete(NEXT_ROUTER_STATE_TREE_HEADER)\n    // When an action follows a redirect, it's no longer handling an action: it's just a normal RSC request\n    // to the requested URL. We should remove the `next-action` header so that it's not treated as an action\n    forwardedHeaders.delete(ACTION_HEADER)\n\n    try {\n      setCacheBustingSearchParam(fetchUrl, {\n        [NEXT_ROUTER_PREFETCH_HEADER]: forwardedHeaders.get(\n          NEXT_ROUTER_PREFETCH_HEADER\n        )\n          ? ('1' as const)\n          : undefined,\n        [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]:\n          forwardedHeaders.get(NEXT_ROUTER_SEGMENT_PREFETCH_HEADER) ??\n          undefined,\n        [NEXT_ROUTER_STATE_TREE_HEADER]:\n          forwardedHeaders.get(NEXT_ROUTER_STATE_TREE_HEADER) ?? undefined,\n        [NEXT_URL]: forwardedHeaders.get(NEXT_URL) ?? undefined,\n      })\n\n      const response = await fetch(fetchUrl, {\n        method: 'GET',\n        headers: forwardedHeaders,\n        next: {\n          // @ts-ignore\n          internal: 1,\n        },\n      })\n\n      if (\n        response.headers\n          .get('content-type')\n          ?.startsWith(RSC_CONTENT_TYPE_HEADER)\n      ) {\n        // copy the headers from the redirect response to the response we're sending\n        for (const [key, value] of response.headers) {\n          if (!actionsForbiddenHeaders.includes(key)) {\n            res.setHeader(key, value)\n          }\n        }\n\n        return new FlightRenderResult(response.body!)\n      } else {\n        // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n        response.body?.cancel()\n      }\n    } catch (err) {\n      // we couldn't stream the redirect response, so we'll just do a normal redirect\n      console.error(`failed to get redirect response`, err)\n    }\n  }\n\n  return RenderResult.fromStatic('')\n}\n\n// Used to compare Host header and Origin header.\nconst enum HostType {\n  XForwardedHost = 'x-forwarded-host',\n  Host = 'host',\n}\ntype Host =\n  | {\n      type: HostType.XForwardedHost\n      value: string\n    }\n  | {\n      type: HostType.Host\n      value: string\n    }\n  | undefined\n\n/**\n * Ensures the value of the header can't create long logs.\n */\nfunction limitUntrustedHeaderValueForLogs(value: string) {\n  return value.length > 100 ? value.slice(0, 100) + '...' : value\n}\n\nexport function parseHostHeader(\n  headers: IncomingHttpHeaders,\n  originDomain?: string\n) {\n  const forwardedHostHeader = headers['x-forwarded-host']\n  const forwardedHostHeaderValue =\n    forwardedHostHeader && Array.isArray(forwardedHostHeader)\n      ? forwardedHostHeader[0]\n      : forwardedHostHeader?.split(',')?.[0]?.trim()\n  const hostHeader = headers['host']\n\n  if (originDomain) {\n    return forwardedHostHeaderValue === originDomain\n      ? {\n          type: HostType.XForwardedHost,\n          value: forwardedHostHeaderValue,\n        }\n      : hostHeader === originDomain\n        ? {\n            type: HostType.Host,\n            value: hostHeader,\n          }\n        : undefined\n  }\n\n  return forwardedHostHeaderValue\n    ? {\n        type: HostType.XForwardedHost,\n        value: forwardedHostHeaderValue,\n      }\n    : hostHeader\n      ? {\n          type: HostType.Host,\n          value: hostHeader,\n        }\n      : undefined\n}\n\ntype ServerModuleMap = Record<\n  string,\n  {\n    id: string\n    chunks: string[]\n    name: string\n  }\n>\n\ntype ServerActionsConfig = {\n  bodySizeLimit?: SizeLimit\n  allowedOrigins?: string[]\n}\n\ntype HandleActionResult =\n  | {\n      /** An MPA action threw notFound(), and we need to render the appropriate HTML */\n      type: 'not-found'\n    }\n  | {\n      type: 'done'\n      result: RenderResult | undefined\n      formState?: any\n    }\n  /** The request turned out not to be a server action. */\n  | null\n\nexport async function handleAction({\n  req,\n  res,\n  ComponentMod,\n  serverModuleMap,\n  generateFlight,\n  workStore,\n  requestStore,\n  serverActions,\n  ctx,\n  metadata,\n}: {\n  req: BaseNextRequest\n  res: BaseNextResponse\n  ComponentMod: AppPageModule\n  serverModuleMap: ServerModuleMap\n  generateFlight: GenerateFlight\n  workStore: WorkStore\n  requestStore: RequestStore\n  serverActions?: ServerActionsConfig\n  ctx: AppRenderContext\n  metadata: AppPageRenderResultMetadata\n}): Promise<HandleActionResult> {\n  const contentType = req.headers['content-type']\n  const { serverActionsManifest, page } = ctx.renderOpts\n\n  const {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  } = getServerActionRequestMetadata(req)\n\n  // If it can't be a Server Action, skip handling.\n  // Note that this can be a false positive -- any multipart/urlencoded POST can get us here,\n  // But won't know if it's an MPA action or not until we call `decodeAction` below.\n  if (!isPossibleServerAction) {\n    return null\n  }\n\n  if (workStore.isStaticGeneration) {\n    throw new Error(\n      \"Invariant: server actions can't be handled during static rendering\"\n    )\n  }\n\n  let temporaryReferences: TemporaryReferenceSet | undefined\n\n  // When running actions the default is no-store, you can still `cache: 'force-cache'`\n  workStore.fetchCache = 'default-no-store'\n\n  const originDomain =\n    typeof req.headers['origin'] === 'string'\n      ? new URL(req.headers['origin']).host\n      : undefined\n  const host = parseHostHeader(req.headers)\n\n  let warning: string | undefined = undefined\n\n  function warnBadServerActionRequest() {\n    if (warning) {\n      warn(warning)\n    }\n  }\n  // This is to prevent CSRF attacks. If `x-forwarded-host` is set, we need to\n  // ensure that the request is coming from the same host.\n  if (!originDomain) {\n    // This might be an old browser that doesn't send `host` header. We ignore\n    // this case.\n    warning = 'Missing `origin` header from a forwarded Server Actions request.'\n  } else if (!host || originDomain !== host.value) {\n    // If the customer sets a list of allowed origins, we'll allow the request.\n    // These are considered safe but might be different from forwarded host set\n    // by the infra (i.e. reverse proxies).\n    if (isCsrfOriginAllowed(originDomain, serverActions?.allowedOrigins)) {\n      // Ignore it\n    } else {\n      if (host) {\n        // This seems to be an CSRF attack. We should not proceed the action.\n        console.error(\n          `\\`${\n            host.type\n          }\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            host.value\n          )}\\` does not match \\`origin\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            originDomain\n          )}\\` from a forwarded Server Actions request. Aborting the action.`\n        )\n      } else {\n        // This is an attack. We should not proceed the action.\n        console.error(\n          `\\`x-forwarded-host\\` or \\`host\\` headers are not provided. One of these is needed to compare the \\`origin\\` header from a forwarded Server Actions request. Aborting the action.`\n        )\n      }\n\n      const error = new Error('Invalid Server Actions request.')\n\n      if (isFetchAction) {\n        res.statusCode = 500\n        metadata.statusCode = 500\n\n        const promise = Promise.reject(error)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n\n        return {\n          type: 'done',\n          result: await generateFlight(req, ctx, requestStore, {\n            actionResult: promise,\n            // We didn't execute an action, so no revalidations could have occurred. We can skip rendering the page.\n            skipFlight: true,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      throw error\n    }\n  }\n\n  // ensure we avoid caching server actions unexpectedly\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  const { actionAsyncStorage } = ComponentMod\n\n  const actionWasForwarded = Boolean(req.headers['x-action-forwarded'])\n\n  if (actionId) {\n    const forwardedWorker = selectWorkerForForwarding(\n      actionId,\n      page,\n      serverActionsManifest\n    )\n\n    // If forwardedWorker is truthy, it means there isn't a worker for the action\n    // in the current handler, so we forward the request to a worker that has the action.\n    if (forwardedWorker) {\n      return {\n        type: 'done',\n        result: await createForwardedActionResponse(\n          req,\n          res,\n          host,\n          forwardedWorker,\n          ctx.renderOpts.basePath\n        ),\n      }\n    }\n  }\n\n  const handleUnrecognizedFetchAction = (err: unknown): HandleActionResult => {\n    // If the deployment doesn't have skew protection, this is expected to occasionally happen,\n    // so we use a warning instead of an error.\n    console.warn(err)\n\n    // Return an empty response with a header that the client router will interpret.\n    // We don't need to waste time encoding a flight response, and using a blank body + header\n    // means that unrecognized actions can also be handled at the infra level\n    // (i.e. without needing to invoke a lambda)\n    res.setHeader(NEXT_ACTION_NOT_FOUND_HEADER, '1')\n    res.setHeader('content-type', 'text/plain')\n    res.statusCode = 404\n    return {\n      type: 'done',\n      result: RenderResult.fromStatic('Server action not found.'),\n    }\n  }\n\n  try {\n    return await actionAsyncStorage.run(\n      { isAction: true },\n      async (): Promise<HandleActionResult> => {\n        // We only use these for fetch actions -- MPA actions handle them inside `decodeAction`.\n        let actionModId: string | undefined\n        let boundActionArguments: unknown[] = []\n\n        if (\n          // The type check here ensures that `req` is correctly typed, and the\n          // environment variable check provides dead code elimination.\n          process.env.NEXT_RUNTIME === 'edge' &&\n          isWebNextRequest(req)\n        ) {\n          if (!req.body) {\n            throw new Error('invariant: Missing request body.')\n          }\n\n          // TODO: add body limit\n\n          // Use react-server-dom-webpack/server\n          const {\n            createTemporaryReferenceSet,\n            decodeReply,\n            decodeAction,\n            decodeFormState,\n          } = ComponentMod\n\n          temporaryReferences = createTemporaryReferenceSet()\n\n          if (isMultipartAction) {\n            // TODO-APP: Add streaming support\n            const formData = await req.request.formData()\n            if (isFetchAction) {\n              // A fetch action with a multipart body.\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              // Multipart POST, but not a fetch action.\n              // Potentially an MPA action, we have to try decoding it to check.\n              const action = await decodeAction(formData, serverModuleMap)\n              if (typeof action === 'function') {\n                // an MPA action.\n\n                // Only warn if it's a server action, otherwise skip for other post requests\n                warnBadServerActionRequest()\n\n                const actionReturnedState =\n                  await executeActionAndPrepareForRender(\n                    action as () => Promise<unknown>,\n                    [],\n                    workStore,\n                    requestStore\n                  )\n\n                const formState = await decodeFormState(\n                  actionReturnedState,\n                  formData,\n                  serverModuleMap\n                )\n\n                // Skip the fetch path.\n                // We need to render a full HTML version of the page for the response, we'll handle that in app-render.\n                return {\n                  type: 'done',\n                  result: undefined,\n                  formState,\n                }\n              } else {\n                // We couldn't decode an action, so this POST request turned out not to be a server action request.\n                return null\n              }\n            }\n          } else {\n            // POST with non-multipart body.\n\n            // If it's not multipart AND not a fetch action,\n            // then it can't be an action request.\n            if (!isFetchAction) {\n              return null\n            }\n\n            try {\n              actionModId = getActionModIdOrError(actionId, serverModuleMap)\n            } catch (err) {\n              return handleUnrecognizedFetchAction(err)\n            }\n\n            // A fetch action with a non-multipart body.\n            // In practice, this happens if `encodeReply` returned a string instead of FormData,\n            // which can happen for very simple JSON-like values that don't need multiple flight rows.\n\n            const chunks: Buffer[] = []\n            const reader = req.body.getReader()\n            while (true) {\n              const { done, value } = await reader.read()\n              if (done) {\n                break\n              }\n\n              chunks.push(value)\n            }\n\n            const actionData = Buffer.concat(chunks).toString('utf-8')\n\n            if (isURLEncodedAction) {\n              const formData = formDataFromSearchQueryString(actionData)\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              boundActionArguments = await decodeReply(\n                actionData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            }\n          }\n        } else if (\n          // The type check here ensures that `req` is correctly typed, and the\n          // environment variable check provides dead code elimination.\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          isNodeNextRequest(req)\n        ) {\n          // Use react-server-dom-webpack/server.node which supports streaming\n          const {\n            createTemporaryReferenceSet,\n            decodeReply,\n            decodeReplyFromBusboy,\n            decodeAction,\n            decodeFormState,\n          } = require(\n            `./react-server.node`\n          ) as typeof import('./react-server.node')\n\n          temporaryReferences = createTemporaryReferenceSet()\n\n          const { Transform, pipeline } =\n            require('node:stream') as typeof import('node:stream')\n\n          const defaultBodySizeLimit = '1 MB'\n          const bodySizeLimit =\n            serverActions?.bodySizeLimit ?? defaultBodySizeLimit\n          const bodySizeLimitBytes =\n            bodySizeLimit !== defaultBodySizeLimit\n              ? (\n                  require('next/dist/compiled/bytes') as typeof import('next/dist/compiled/bytes')\n                ).parse(bodySizeLimit)\n              : 1024 * 1024 // 1 MB\n\n          let size = 0\n          const sizeLimitTransform = new Transform({\n            transform(chunk, encoding, callback) {\n              size += Buffer.byteLength(chunk, encoding)\n              if (size > bodySizeLimitBytes) {\n                const { ApiError } =\n                  require('../api-utils') as typeof import('../api-utils')\n\n                callback(\n                  new ApiError(\n                    413,\n                    `Body exceeded ${bodySizeLimit} limit.\\n` +\n                      `To configure the body size limit for Server Actions, see: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit`\n                  )\n                )\n                return\n              }\n\n              callback(null, chunk)\n            },\n          })\n\n          const sizeLimitedBody = pipeline(\n            req.body,\n            sizeLimitTransform,\n            // Avoid unhandled errors from `pipeline()` by passing an empty completion callback.\n            // We'll propagate the errors properly when consuming the stream.\n            () => {}\n          )\n\n          if (isMultipartAction) {\n            if (isFetchAction) {\n              // A fetch action with a multipart body.\n\n              const busboy = (\n                require('next/dist/compiled/busboy') as typeof import('next/dist/compiled/busboy')\n              )({\n                defParamCharset: 'utf8',\n                headers: req.headers,\n                limits: { fieldSize: bodySizeLimitBytes },\n              })\n\n              // We need to use `pipeline(one, two)` instead of `one.pipe(two)` to propagate size limit errors correctly.\n              pipeline(\n                sizeLimitedBody,\n                busboy,\n                // Avoid unhandled errors from `pipeline()` by passing an empty completion callback.\n                // We'll propagate the errors properly when consuming the stream.\n                () => {}\n              )\n\n              boundActionArguments = await decodeReplyFromBusboy(\n                busboy,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              // Multipart POST, but not a fetch action.\n              // Potentially an MPA action, we have to try decoding it to check.\n\n              // React doesn't yet publish a busboy version of decodeAction\n              // so we polyfill the parsing of FormData.\n              const fakeRequest = new Request('http://localhost', {\n                method: 'POST',\n                // @ts-expect-error\n                headers: { 'Content-Type': contentType },\n                body: new ReadableStream({\n                  start: (controller) => {\n                    sizeLimitedBody.on('data', (chunk) => {\n                      controller.enqueue(new Uint8Array(chunk))\n                    })\n                    sizeLimitedBody.on('end', () => {\n                      controller.close()\n                    })\n                    sizeLimitedBody.on('error', (err) => {\n                      controller.error(err)\n                    })\n                  },\n                }),\n                duplex: 'half',\n              })\n              const formData = await fakeRequest.formData()\n              const action = await decodeAction(formData, serverModuleMap)\n              if (typeof action === 'function') {\n                // an MPA action.\n\n                // Only warn if it's a server action, otherwise skip for other post requests\n                warnBadServerActionRequest()\n\n                const actionReturnedState =\n                  await executeActionAndPrepareForRender(\n                    action as () => Promise<unknown>,\n                    [],\n                    workStore,\n                    requestStore\n                  )\n\n                const formState = await decodeFormState(\n                  actionReturnedState,\n                  formData,\n                  serverModuleMap\n                )\n\n                // Skip the fetch path.\n                // We need to render a full HTML version of the page for the response, we'll handle that in app-render.\n                return {\n                  type: 'done',\n                  result: undefined,\n                  formState,\n                }\n              } else {\n                // We couldn't decode an action, so this POST request turned out not to be a server action request.\n                return null\n              }\n            }\n          } else {\n            // POST with non-multipart body.\n\n            // If it's not multipart AND not a fetch action,\n            // then it can't be an action request.\n            if (!isFetchAction) {\n              return null\n            }\n\n            try {\n              actionModId = getActionModIdOrError(actionId, serverModuleMap)\n            } catch (err) {\n              return handleUnrecognizedFetchAction(err)\n            }\n\n            // A fetch action with a non-multipart body.\n            // In practice, this happens if `encodeReply` returned a string instead of FormData,\n            // which can happen for very simple JSON-like values that don't need multiple flight rows.\n\n            const chunks: Buffer[] = []\n            for await (const chunk of sizeLimitedBody) {\n              chunks.push(Buffer.from(chunk))\n            }\n\n            const actionData = Buffer.concat(chunks).toString('utf-8')\n\n            if (isURLEncodedAction) {\n              const formData = formDataFromSearchQueryString(actionData)\n              boundActionArguments = await decodeReply(\n                formData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            } else {\n              boundActionArguments = await decodeReply(\n                actionData,\n                serverModuleMap,\n                { temporaryReferences }\n              )\n            }\n          }\n        } else {\n          throw new Error('Invariant: Unknown request type.')\n        }\n\n        // actions.js\n        // app/page.js\n        //   action worker1\n        //     appRender1\n\n        // app/foo/page.js\n        //   action worker2\n        //     appRender\n\n        // / -> fire action -> POST / -> appRender1 -> modId for the action file\n        // /foo -> fire action -> POST /foo -> appRender2 -> modId for the action file\n\n        try {\n          actionModId =\n            actionModId ?? getActionModIdOrError(actionId, serverModuleMap)\n        } catch (err) {\n          return handleUnrecognizedFetchAction(err)\n        }\n\n        const actionMod = (await ComponentMod.__next_app__.require(\n          actionModId\n        )) as Record<string, (...args: unknown[]) => Promise<unknown>>\n        const actionHandler =\n          actionMod[\n            // `actionId` must exist if we got here, as otherwise we would have thrown an error above\n            actionId!\n          ]\n\n        const returnVal = await executeActionAndPrepareForRender(\n          actionHandler,\n          boundActionArguments,\n          workStore,\n          requestStore\n        ).finally(() => {\n          addRevalidationHeader(res, { workStore, requestStore })\n        })\n\n        // For form actions, we need to continue rendering the page.\n        if (isFetchAction) {\n          const actionResult = await generateFlight(req, ctx, requestStore, {\n            actionResult: Promise.resolve(returnVal),\n            // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n            skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n            temporaryReferences,\n          })\n\n          return {\n            type: 'done',\n            result: actionResult,\n          }\n        } else {\n          // TODO: this shouldn't be reachable, because all non-fetch codepaths return early.\n          // this will be handled in a follow-up refactor PR.\n          return null\n        }\n      }\n    )\n  } catch (err) {\n    if (isRedirectError(err)) {\n      const redirectUrl = getURLFromRedirectError(err)\n      const redirectType = getRedirectTypeFromError(err)\n\n      // if it's a fetch action, we'll set the status code for logging/debugging purposes\n      // but we won't set a Location header, as the redirect will be handled by the client router\n      res.statusCode = RedirectStatusCode.SeeOther\n      metadata.statusCode = RedirectStatusCode.SeeOther\n\n      if (isFetchAction) {\n        return {\n          type: 'done',\n          result: await createRedirectRenderResult(\n            req,\n            res,\n            host,\n            redirectUrl,\n            redirectType,\n            ctx.renderOpts.basePath,\n            workStore\n          ),\n        }\n      }\n\n      // For an MPA action, the redirect doesn't need a body, just a Location header.\n      res.setHeader('Location', redirectUrl)\n      return {\n        type: 'done',\n        result: RenderResult.fromStatic(''),\n      }\n    } else if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n\n      if (isFetchAction) {\n        const promise = Promise.reject(err)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n        return {\n          type: 'done',\n          result: await generateFlight(req, ctx, requestStore, {\n            skipFlight: false,\n            actionResult: promise,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      // For an MPA action, we need to render a HTML response. We'll handle that in app-render.\n      return {\n        type: 'not-found',\n      }\n    }\n\n    // An error that didn't come from `redirect()` or `notFound()`, likely thrown from user code\n    // (but it could also be a bug in our code!)\n\n    if (isFetchAction) {\n      // TODO: consider checking if the error is an `ApiError` and change status code\n      // so that we can respond with a 413 to requests that break the body size limit\n      // (but if we do that, we also need to make sure that whatever handles the non-fetch error path below does the same)\n      res.statusCode = 500\n      metadata.statusCode = 500\n      const promise = Promise.reject(err)\n      try {\n        // we need to await the promise to trigger the rejection early\n        // so that it's already handled by the time we call\n        // the RSC runtime. Otherwise, it will throw an unhandled\n        // promise rejection error in the renderer.\n        await promise\n      } catch {\n        // swallow error, it's gonna be handled on the client\n      }\n\n      return {\n        type: 'done',\n        result: await generateFlight(req, ctx, requestStore, {\n          actionResult: promise,\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        }),\n      }\n    }\n\n    // For an MPA action, we need to render a HTML response. We'll rethrow the error and let it be handled above.\n    throw err\n  }\n}\n\nasync function executeActionAndPrepareForRender<\n  TFn extends (...args: any[]) => Promise<any>,\n>(\n  action: TFn,\n  args: Parameters<TFn>,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Awaited<ReturnType<TFn>>> {\n  requestStore.phase = 'action'\n  try {\n    return await workUnitAsyncStorage.run(requestStore, () =>\n      action.apply(null, args)\n    )\n  } finally {\n    requestStore.phase = 'render'\n\n    // When we switch to the render phase, cookies() will return\n    // `workUnitStore.cookies` instead of `workUnitStore.userspaceMutableCookies`.\n    // We want the render to see any cookie writes that we performed during the action,\n    // so we need to update the immutable cookies to reflect the changes.\n    synchronizeMutableCookies(requestStore)\n\n    // The server action might have toggled draft mode, so we need to reflect\n    // that in the work store to be up-to-date for subsequent rendering.\n    workStore.isDraftMode = requestStore.draftMode.isEnabled\n\n    // If the action called revalidateTag/revalidatePath, then that might affect data used by the subsequent render,\n    // so we need to make sure all revalidations are applied before that\n    await executeRevalidates(workStore)\n  }\n}\n\n/**\n * Attempts to find the module ID for the action from the module map. When this fails, it could be a deployment skew where\n * the action came from a different deployment. It could also simply be an invalid POST request that is not a server action.\n * In either case, we'll throw an error to be handled by the caller.\n */\nfunction getActionModIdOrError(\n  actionId: string | null,\n  serverModuleMap: ServerModuleMap\n): string {\n  // if we're missing the action ID header, we can't do any further processing\n  if (!actionId) {\n    throw new InvariantError(\"Missing 'next-action' header.\")\n  }\n\n  const actionModId = serverModuleMap[actionId]?.id\n\n  if (!actionModId) {\n    throw new Error(\n      `Failed to find Server Action \"${actionId}\". This request might be from an older or newer deployment.\\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  return actionModId\n}\n"], "names": ["handleAction", "parseHostHeader", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "responseHeaders", "getHeaders", "responseCookies", "ResponseCookies", "fromNodeOutgoingHttpHeaders", "mergedHeaders", "filterReqHeaders", "actionsForbiddenHeaders", "getAll", "for<PERSON>ach", "cookie", "delete", "name", "set", "toString", "Headers", "addRevalidationHeader", "workStore", "requestStore", "isTagRevalidated", "pendingRevalidatedTags", "length", "isCookieRevalidated", "getModifiedCookieValues", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createForwardedActionResponse", "host", "workerPathname", "basePath", "getRequestMeta", "Error", "forwardedHeaders", "proto", "replace", "origin", "process", "env", "__NEXT_PRIVATE_ORIGIN", "fetchUrl", "URL", "response", "body", "NEXT_RUNTIME", "isWebNextRequest", "isNodeNextRequest", "stream", "fetch", "method", "duplex", "redirect", "next", "internal", "get", "startsWith", "RSC_CONTENT_TYPE_HEADER", "includes", "FlightRenderResult", "cancel", "err", "console", "error", "RenderResult", "fromStatic", "getAppRelativeRedirectUrl", "redirectUrl", "parsedRedirectUrl", "pathname", "createRedirectRenderResult", "originalHost", "redirectType", "appRelativeRedirectUrl", "RSC_HEADER", "search", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "incrementalCache", "prerenderManifest", "preview", "previewModeId", "NEXT_ROUTER_STATE_TREE_HEADER", "ACTION_HEADER", "setCacheBustingSearchParam", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_URL", "limitUntrustedHeaderValueForLogs", "slice", "originDomain", "forwarded<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedHostHeaderValue", "split", "trim", "<PERSON><PERSON><PERSON><PERSON>", "type", "ComponentMod", "serverModuleMap", "generateFlight", "serverActions", "ctx", "metadata", "contentType", "serverActionsManifest", "page", "renderOpts", "actionId", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isPossibleServerAction", "getServerActionRequestMetadata", "isStaticGeneration", "temporaryReferences", "fetchCache", "warning", "warnBadServerActionRequest", "warn", "isCsrfOriginAllowed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "promise", "Promise", "reject", "result", "actionResult", "skipFlight", "actionAsyncStorage", "actionWasForwarded", "Boolean", "forwarded<PERSON><PERSON><PERSON>", "selectWorkerForForwarding", "handleUnrecognizedFetchAction", "NEXT_ACTION_NOT_FOUND_HEADER", "run", "isAction", "actionModId", "boundActionArguments", "createTemporaryReferenceSet", "decodeReply", "decodeAction", "decodeFormState", "request", "action", "actionReturnedState", "executeActionAndPrepareForRender", "formState", "getActionModIdOrError", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "push", "actionData", "<PERSON><PERSON><PERSON>", "concat", "decodeReplyFromBusboy", "require", "Transform", "pipeline", "defaultBodySizeLimit", "bodySizeLimit", "bodySizeLimitBytes", "parse", "size", "sizeLimitTransform", "transform", "chunk", "encoding", "callback", "byteLength", "ApiError", "sizeLimitedBody", "busboy", "def<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limits", "fieldSize", "fakeRequest", "Request", "ReadableStream", "start", "controller", "on", "enqueue", "Uint8Array", "close", "actionMod", "__next_app__", "actionHandler", "returnVal", "finally", "resolve", "pathWasRevalidated", "isRedirectError", "getURLFromRedirectError", "getRedirectTypeFromError", "RedirectStatusCode", "<PERSON><PERSON><PERSON>", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "args", "phase", "workUnitAsyncStorage", "apply", "synchronizeMutableCookies", "isDraftMode", "draftMode", "isEnabled", "executeRevalidates", "InvariantError", "id"], "mappings": ";;;;;;;;;;;;;;;IAkesBA,YAAY;eAAZA;;IAjENC,eAAe;eAAfA;;;kCAjZT;oCAIA;0BAIA;+BAIA;qEAGA;oCAE4B;uBAI5B;gCACiC;2BAKjC;yCACwC;gCACX;qBACf;yBAC2B;yBACjB;wBACa;6BACF;yBACU;oCACjB;8BACO;8CAEL;gCACN;mCACI;6BACJ;4CACY;;;;;;AAE3C,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,GAAGV,OAAO;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAqB;IAErB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiB,IAAIC,uBAAc,CAACC,uBAAc,CAACC,IAAI,CAACJ;IAE9D,mCAAmC;IACnC,MAAMK,kBAAkBN,IAAIO,UAAU;IACtC,MAAMC,kBAAkB,IAAIC,wBAAe,CACzCC,IAAAA,mCAA2B,EAACJ;IAG9B,qCAAqC;IACrC,MAAMK,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,GAAGvB,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBiB,gBAAgB;IACzC,GACAO,8BAAuB;IAGzB,+EAA+E;IAC/E,kDAAkD;IAClDL,gBAAgBM,MAAM,GAAGC,OAAO,CAAC,CAACC;QAChC,IAAI,OAAOA,OAAO7B,KAAK,KAAK,aAAa;YACvCe,eAAee,MAAM,CAACD,OAAOE,IAAI;QACnC,OAAO;YACLhB,eAAeiB,GAAG,CAACH;QACrB;IACF;IAEA,qDAAqD;IACrDL,aAAa,CAAC,SAAS,GAAGT,eAAekB,QAAQ;IAEjD,8CAA8C;IAC9C,OAAOT,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIU,QAAQV;AACrB;AAEA,SAASW,sBACPtB,GAAqB,EACrB,EACEuB,SAAS,EACTC,YAAY,EAIb;QAewBD;IAbzB,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAME,mBAAmBF,EAAAA,oCAAAA,UAAUG,sBAAsB,qBAAhCH,kCAAkCI,MAAM,IAAG,IAAI;IACxE,MAAMC,sBAAsBC,IAAAA,uCAAuB,EACjDL,aAAaM,cAAc,EAC3BH,MAAM,GACJ,IACA;IAEJ3B,IAAI+B,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAER;QAAkBG;KAAoB;AAE9D;AAEA;;CAEC,GACD,eAAeM,8BACbnC,GAAoB,EACpBC,GAAqB,EACrBmC,IAAU,EACVC,cAAsB,EACtBC,QAAgB;QAgBdC;IAdF,IAAI,CAACH,MAAM;QACT,MAAM,qBAEL,CAFK,IAAII,MACR,8EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,mBAAmB1C,oBAAoBC,KAAKC;IAElD,sEAAsE;IACtE,+EAA+E;IAC/E,8CAA8C;IAC9CwC,iBAAiBrB,GAAG,CAAC,sBAAsB;IAE3C,MAAMsB,QACJH,EAAAA,kBAAAA,IAAAA,2BAAc,EAACvC,KAAK,oCAApBuC,gBAAqCI,OAAO,CAAC,OAAO,QAAO;IAE7D,yEAAyE;IACzE,gDAAgD;IAChD,MAAMC,SAASC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAEN,KAAKhD,KAAK,EAAE;IAE9E,MAAM4D,WAAW,IAAIC,IAAI,GAAGL,SAASN,WAAWD,gBAAgB;IAEhE,IAAI;YAsCAa;QArCF,IAAIC;QACJ,IACE,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAACrD,MACjB;YACA,IAAI,CAACA,IAAImD,IAAI,EAAE;gBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEAW,OAAOnD,IAAImD,IAAI;QACjB,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAACtD,MAClB;YACAmD,OAAOnD,IAAIuD,MAAM;QACnB,OAAO;YACL,MAAM,qBAA6C,CAA7C,IAAIf,MAAM,qCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACpD;QAEA,wCAAwC;QACxC,MAAMU,WAAW,MAAMM,MAAMR,UAAU;YACrCS,QAAQ;YACRN;YACAO,QAAQ;YACRnE,SAASkD;YACTkB,UAAU;YACVC,MAAM;gBACJ,aAAa;gBACbC,UAAU;YACZ;QACF;QAEA,KACEX,wBAAAA,SAAS3D,OAAO,CAACuE,GAAG,CAAC,oCAArBZ,sBAAsCa,UAAU,CAACC,yCAAuB,GACxE;YACA,4EAA4E;YAC5E,KAAK,MAAM,CAAC7E,KAAKC,MAAM,IAAI8D,SAAS3D,OAAO,CAAE;gBAC3C,IAAI,CAACuB,8BAAuB,CAACmD,QAAQ,CAAC9E,MAAM;oBAC1Cc,IAAI+B,SAAS,CAAC7C,KAAKC;gBACrB;YACF;YAEA,OAAO,IAAI8E,sCAAkB,CAAChB,SAASC,IAAI;QAC7C,OAAO;gBACL,kFAAkF;YAClFD;aAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;QACvB;IACF,EAAE,OAAOC,KAAK;QACZ,oFAAoF;QACpFC,QAAQC,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAEF;IACrD;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAEA;;;;;;CAMC,GACD,SAASC,0BACPnC,QAAgB,EAChBF,IAAU,EACVsC,WAAmB;IAEnB,IAAIA,YAAYX,UAAU,CAAC,QAAQW,YAAYX,UAAU,CAAC,MAAM;QAC9D,2DAA2D;QAC3D,OAAO,IAAId,IAAI,GAAGX,WAAWoC,aAAa,EAAE;IAC9C;IAEA,MAAMC,oBAAoB,IAAI1B,IAAIyB;IAElC,IAAItC,CAAAA,wBAAAA,KAAMhD,KAAK,MAAKuF,kBAAkBvC,IAAI,EAAE;QAC1C,OAAO;IACT;IAEA,wDAAwD;IACxD,kDAAkD;IAClD,OAAOuC,kBAAkBC,QAAQ,CAACb,UAAU,CAACzB,YACzCqC,oBACA;AACN;AAEA,eAAeE,2BACb7E,GAAoB,EACpBC,GAAqB,EACrB6E,YAAkB,EAClBJ,WAAmB,EACnBK,YAA0B,EAC1BzC,QAAgB,EAChBd,SAAoB;IAEpBvB,IAAI+B,SAAS,CAAC,qBAAqB,GAAG0C,YAAY,CAAC,EAAEK,cAAc;IAEnE,2EAA2E;IAC3E,0EAA0E;IAC1E,+DAA+D;IAC/D,4EAA4E;IAC5E,sDAAsD;IACtD,MAAMC,yBAAyBP,0BAC7BnC,UACAwC,cACAJ;IAGF,IAAIM,wBAAwB;YAWxBzC;QAVF,IAAI,CAACuC,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAItC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,mBAAmB1C,oBAAoBC,KAAKC;QAClDwC,iBAAiBrB,GAAG,CAAC6D,4BAAU,EAAE;QAEjC,MAAMvC,QACJH,EAAAA,kBAAAA,IAAAA,2BAAc,EAACvC,KAAK,oCAApBuC,gBAAqCI,OAAO,CAAC,OAAO,QAAO;QAE7D,yEAAyE;QACzE,gDAAgD;QAChD,MAAMC,SACJC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAEoC,aAAa1F,KAAK,EAAE;QAEzE,MAAM4D,WAAW,IAAIC,IACnB,GAAGL,SAASoC,uBAAuBJ,QAAQ,GAAGI,uBAAuBE,MAAM,EAAE;QAG/E,IAAI1D,UAAUG,sBAAsB,EAAE;gBAOlCH,uDAAAA,+CAAAA;YANFiB,iBAAiBrB,GAAG,CAClB+D,6CAAkC,EAClC3D,UAAUG,sBAAsB,CAAC7B,IAAI,CAAC;YAExC2C,iBAAiBrB,GAAG,CAClBgE,iDAAsC,EACtC5D,EAAAA,8BAAAA,UAAU6D,gBAAgB,sBAA1B7D,gDAAAA,4BAA4B8D,iBAAiB,sBAA7C9D,wDAAAA,8CAA+C+D,OAAO,qBAAtD/D,sDAAwDgE,aAAa,KACnE;QAEN;QAEA,6FAA6F;QAC7F/C,iBAAiBvB,MAAM,CAACuE,+CAA6B;QACrD,uGAAuG;QACvG,wGAAwG;QACxGhD,iBAAiBvB,MAAM,CAACwE,+BAAa;QAErC,IAAI;gBAyBAxC;YAxBFyC,IAAAA,sDAA0B,EAAC3C,UAAU;gBACnC,CAAC4C,6CAA2B,CAAC,EAAEnD,iBAAiBqB,GAAG,CACjD8B,6CAA2B,IAExB,MACDjG;gBACJ,CAACkG,qDAAmC,CAAC,EACnCpD,iBAAiBqB,GAAG,CAAC+B,qDAAmC,KACxDlG;gBACF,CAAC8F,+CAA6B,CAAC,EAC7BhD,iBAAiBqB,GAAG,CAAC2B,+CAA6B,KAAK9F;gBACzD,CAACmG,0BAAQ,CAAC,EAAErD,iBAAiBqB,GAAG,CAACgC,0BAAQ,KAAKnG;YAChD;YAEA,MAAMuD,WAAW,MAAMM,MAAMR,UAAU;gBACrCS,QAAQ;gBACRlE,SAASkD;gBACTmB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,KACEX,wBAAAA,SAAS3D,OAAO,CACbuE,GAAG,CAAC,oCADPZ,sBAEIa,UAAU,CAACC,yCAAuB,GACtC;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAAC7E,KAAKC,MAAM,IAAI8D,SAAS3D,OAAO,CAAE;oBAC3C,IAAI,CAACuB,8BAAuB,CAACmD,QAAQ,CAAC9E,MAAM;wBAC1Cc,IAAI+B,SAAS,CAAC7C,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAI8E,sCAAkB,CAAChB,SAASC,IAAI;YAC7C,OAAO;oBACL,kFAAkF;gBAClFD;iBAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;YACvB;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAkBA;;CAEC,GACD,SAASuB,iCAAiC3G,KAAa;IACrD,OAAOA,MAAMwC,MAAM,GAAG,MAAMxC,MAAM4G,KAAK,CAAC,GAAG,OAAO,QAAQ5G;AAC5D;AAEO,SAASR,gBACdW,OAA4B,EAC5B0G,YAAqB;QAMfC,6BAAAA;IAJN,MAAMA,sBAAsB3G,OAAO,CAAC,mBAAmB;IACvD,MAAM4G,2BACJD,uBAAuBtG,MAAMC,OAAO,CAACqG,uBACjCA,mBAAmB,CAAC,EAAE,GACtBA,wCAAAA,6BAAAA,oBAAqBE,KAAK,CAAC,0BAA3BF,8BAAAA,0BAAiC,CAAC,EAAE,qBAApCA,4BAAsCG,IAAI;IAChD,MAAMC,aAAa/G,OAAO,CAAC,OAAO;IAElC,IAAI0G,cAAc;QAChB,OAAOE,6BAA6BF,eAChC;YACEM,IAAI;YACJnH,OAAO+G;QACT,IACAG,eAAeL,eACb;YACEM,IAAI;YACJnH,OAAOkH;QACT,IACA3G;IACR;IAEA,OAAOwG,2BACH;QACEI,IAAI;QACJnH,OAAO+G;IACT,IACAG,aACE;QACEC,IAAI;QACJnH,OAAOkH;IACT,IACA3G;AACR;AA6BO,eAAehB,aAAa,EACjCqB,GAAG,EACHC,GAAG,EACHuG,YAAY,EACZC,eAAe,EACfC,cAAc,EACdlF,SAAS,EACTC,YAAY,EACZkF,aAAa,EACbC,GAAG,EACHC,QAAQ,EAYT;IACC,MAAMC,cAAc9G,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM,EAAEwH,qBAAqB,EAAEC,IAAI,EAAE,GAAGJ,IAAIK,UAAU;IAEtD,MAAM,EACJC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,sBAAsB,EACvB,GAAGC,IAAAA,uDAA8B,EAACvH;IAEnC,iDAAiD;IACjD,2FAA2F;IAC3F,kFAAkF;IAClF,IAAI,CAACsH,wBAAwB;QAC3B,OAAO;IACT;IAEA,IAAI9F,UAAUgG,kBAAkB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIhF,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIiF;IAEJ,qFAAqF;IACrFjG,UAAUkG,UAAU,GAAG;IAEvB,MAAMzB,eACJ,OAAOjG,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAI0D,IAAIjD,IAAIT,OAAO,CAAC,SAAS,EAAE6C,IAAI,GACnCzC;IACN,MAAMyC,OAAOxD,gBAAgBoB,IAAIT,OAAO;IAExC,IAAIoI,UAA8BhI;IAElC,SAASiI;QACP,IAAID,SAAS;YACXE,IAAAA,SAAI,EAACF;QACP;IACF;IACA,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC1B,cAAc;QACjB,0EAA0E;QAC1E,aAAa;QACb0B,UAAU;IACZ,OAAO,IAAI,CAACvF,QAAQ6D,iBAAiB7D,KAAKhD,KAAK,EAAE;QAC/C,2EAA2E;QAC3E,2EAA2E;QAC3E,uCAAuC;QACvC,IAAI0I,IAAAA,mCAAmB,EAAC7B,cAAcU,iCAAAA,cAAeoB,cAAc,GAAG;QACpE,YAAY;QACd,OAAO;YACL,IAAI3F,MAAM;gBACR,qEAAqE;gBACrEiC,QAAQC,KAAK,CACX,CAAC,EAAE,EACDlC,KAAKmE,IAAI,CACV,uBAAuB,EAAER,iCACxB3D,KAAKhD,KAAK,EACV,iDAAiD,EAAE2G,iCACnDE,cACA,gEAAgE,CAAC;YAEvE,OAAO;gBACL,uDAAuD;gBACvD5B,QAAQC,KAAK,CACX,CAAC,gLAAgL,CAAC;YAEtL;YAEA,MAAMA,QAAQ,qBAA4C,CAA5C,IAAI9B,MAAM,oCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA2C;YAEzD,IAAI6E,eAAe;gBACjBpH,IAAI+H,UAAU,GAAG;gBACjBnB,SAASmB,UAAU,GAAG;gBAEtB,MAAMC,UAAUC,QAAQC,MAAM,CAAC7D;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAM2D;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBAEA,OAAO;oBACL1B,MAAM;oBACN6B,QAAQ,MAAM1B,eAAe1G,KAAK4G,KAAKnF,cAAc;wBACnD4G,cAAcJ;wBACd,wGAAwG;wBACxGK,YAAY;wBACZb;oBACF;gBACF;YACF;YAEA,MAAMnD;QACR;IACF;IAEA,sDAAsD;IACtDrE,IAAI+B,SAAS,CACX,iBACA;IAGF,MAAM,EAAEuG,kBAAkB,EAAE,GAAG/B;IAE/B,MAAMgC,qBAAqBC,QAAQzI,IAAIT,OAAO,CAAC,qBAAqB;IAEpE,IAAI2H,UAAU;QACZ,MAAMwB,kBAAkBC,IAAAA,sCAAyB,EAC/CzB,UACAF,MACAD;QAGF,6EAA6E;QAC7E,qFAAqF;QACrF,IAAI2B,iBAAiB;YACnB,OAAO;gBACLnC,MAAM;gBACN6B,QAAQ,MAAMjG,8BACZnC,KACAC,KACAmC,MACAsG,iBACA9B,IAAIK,UAAU,CAAC3E,QAAQ;YAE3B;QACF;IACF;IAEA,MAAMsG,gCAAgC,CAACxE;QACrC,2FAA2F;QAC3F,2CAA2C;QAC3CC,QAAQwD,IAAI,CAACzD;QAEb,gFAAgF;QAChF,0FAA0F;QAC1F,yEAAyE;QACzE,4CAA4C;QAC5CnE,IAAI+B,SAAS,CAAC6G,8CAA4B,EAAE;QAC5C5I,IAAI+B,SAAS,CAAC,gBAAgB;QAC9B/B,IAAI+H,UAAU,GAAG;QACjB,OAAO;YACLzB,MAAM;YACN6B,QAAQ7D,qBAAY,CAACC,UAAU,CAAC;QAClC;IACF;IAEA,IAAI;QACF,OAAO,MAAM+D,mBAAmBO,GAAG,CACjC;YAAEC,UAAU;QAAK,GACjB;YACE,wFAAwF;YACxF,IAAIC;YACJ,IAAIC,uBAAkC,EAAE;YAExC,IACE,qEAAqE;YACrE,6DAA6D;YAC7DpG,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAACrD,MACjB;gBACA,IAAI,CAACA,IAAImD,IAAI,EAAE;oBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;gBAEA,uBAAuB;gBAEvB,sCAAsC;gBACtC,MAAM,EACJ0G,2BAA2B,EAC3BC,WAAW,EACXC,YAAY,EACZC,eAAe,EAChB,GAAG7C;gBAEJiB,sBAAsByB;gBAEtB,IAAI9B,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAMnI,WAAW,MAAMe,IAAIsJ,OAAO,CAACrK,QAAQ;oBAC3C,IAAIoI,eAAe;wBACjB,wCAAwC;wBACxC4B,uBAAuB,MAAME,YAC3BlK,UACAwH,iBACA;4BAAEgB;wBAAoB;oBAE1B,OAAO;wBACL,0CAA0C;wBAC1C,kEAAkE;wBAClE,MAAM8B,SAAS,MAAMH,aAAanK,UAAUwH;wBAC5C,IAAI,OAAO8C,WAAW,YAAY;4BAChC,iBAAiB;4BAEjB,4EAA4E;4BAC5E3B;4BAEA,MAAM4B,sBACJ,MAAMC,iCACJF,QACA,EAAE,EACF/H,WACAC;4BAGJ,MAAMiI,YAAY,MAAML,gBACtBG,qBACAvK,UACAwH;4BAGF,uBAAuB;4BACvB,uGAAuG;4BACvG,OAAO;gCACLF,MAAM;gCACN6B,QAAQzI;gCACR+J;4BACF;wBACF,OAAO;4BACL,mGAAmG;4BACnG,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,gCAAgC;oBAEhC,gDAAgD;oBAChD,sCAAsC;oBACtC,IAAI,CAACrC,eAAe;wBAClB,OAAO;oBACT;oBAEA,IAAI;wBACF2B,cAAcW,sBAAsBzC,UAAUT;oBAChD,EAAE,OAAOrC,KAAK;wBACZ,OAAOwE,8BAA8BxE;oBACvC;oBAEA,4CAA4C;oBAC5C,oFAAoF;oBACpF,0FAA0F;oBAE1F,MAAMwF,SAAmB,EAAE;oBAC3B,MAAMC,SAAS7J,IAAImD,IAAI,CAAC2G,SAAS;oBACjC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAE3K,KAAK,EAAE,GAAG,MAAMyK,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,OAAOK,IAAI,CAAC7K;oBACd;oBAEA,MAAM8K,aAAaC,OAAOC,MAAM,CAACR,QAAQvI,QAAQ,CAAC;oBAElD,IAAI8F,oBAAoB;wBACtB,MAAMlI,WAAWJ,8BAA8BqL;wBAC/CjB,uBAAuB,MAAME,YAC3BlK,UACAwH,iBACA;4BAAEgB;wBAAoB;oBAE1B,OAAO;wBACLwB,uBAAuB,MAAME,YAC3Be,YACAzD,iBACA;4BAAEgB;wBAAoB;oBAE1B;gBACF;YACF,OAAO,IACL,qEAAqE;YACrE,6DAA6D;YAC7D5E,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAACtD,MAClB;gBACA,oEAAoE;gBACpE,MAAM,EACJkJ,2BAA2B,EAC3BC,WAAW,EACXkB,qBAAqB,EACrBjB,YAAY,EACZC,eAAe,EAChB,GAAGiB,QACF,CAAC,mBAAmB,CAAC;gBAGvB7C,sBAAsByB;gBAEtB,MAAM,EAAEqB,SAAS,EAAEC,QAAQ,EAAE,GAC3BF,QAAQ;gBAEV,MAAMG,uBAAuB;gBAC7B,MAAMC,gBACJ/D,CAAAA,iCAAAA,cAAe+D,aAAa,KAAID;gBAClC,MAAME,qBACJD,kBAAkBD,uBACd,AACEH,QAAQ,4BACRM,KAAK,CAACF,iBACR,OAAO,KAAK,OAAO;;gBAEzB,IAAIG,OAAO;gBACX,MAAMC,qBAAqB,IAAIP,UAAU;oBACvCQ,WAAUC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ;wBACjCL,QAAQV,OAAOgB,UAAU,CAACH,OAAOC;wBACjC,IAAIJ,OAAOF,oBAAoB;4BAC7B,MAAM,EAAES,QAAQ,EAAE,GAChBd,QAAQ;4BAEVY,SACE,qBAIC,CAJD,IAAIE,SACF,KACA,CAAC,cAAc,EAAEV,cAAc,SAAS,CAAC,GACvC,CAAC,8IAA8I,CAAC,GAHpJ,qBAAA;uCAAA;4CAAA;8CAAA;4BAIA;4BAEF;wBACF;wBAEAQ,SAAS,MAAMF;oBACjB;gBACF;gBAEA,MAAMK,kBAAkBb,SACtBxK,IAAImD,IAAI,EACR2H,oBACA,oFAAoF;gBACpF,iEAAiE;gBACjE,KAAO;gBAGT,IAAI1D,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,wCAAwC;wBAExC,MAAMiE,SAAS,AACbhB,QAAQ,6BACR;4BACAiB,iBAAiB;4BACjBhM,SAASS,IAAIT,OAAO;4BACpBiM,QAAQ;gCAAEC,WAAWd;4BAAmB;wBAC1C;wBAEA,2GAA2G;wBAC3GH,SACEa,iBACAC,QACA,oFAAoF;wBACpF,iEAAiE;wBACjE,KAAO;wBAGTrC,uBAAuB,MAAMoB,sBAC3BiB,QACA7E,iBACA;4BAAEgB;wBAAoB;oBAE1B,OAAO;wBACL,0CAA0C;wBAC1C,kEAAkE;wBAElE,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAMiE,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDlI,QAAQ;4BACR,mBAAmB;4BACnBlE,SAAS;gCAAE,gBAAgBuH;4BAAY;4BACvC3D,MAAM,IAAIyI,eAAe;gCACvBC,OAAO,CAACC;oCACNT,gBAAgBU,EAAE,CAAC,QAAQ,CAACf;wCAC1Bc,WAAWE,OAAO,CAAC,IAAIC,WAAWjB;oCACpC;oCACAK,gBAAgBU,EAAE,CAAC,OAAO;wCACxBD,WAAWI,KAAK;oCAClB;oCACAb,gBAAgBU,EAAE,CAAC,SAAS,CAAC3H;wCAC3B0H,WAAWxH,KAAK,CAACF;oCACnB;gCACF;4BACF;4BACAV,QAAQ;wBACV;wBACA,MAAMzE,WAAW,MAAMyM,YAAYzM,QAAQ;wBAC3C,MAAMsK,SAAS,MAAMH,aAAanK,UAAUwH;wBAC5C,IAAI,OAAO8C,WAAW,YAAY;4BAChC,iBAAiB;4BAEjB,4EAA4E;4BAC5E3B;4BAEA,MAAM4B,sBACJ,MAAMC,iCACJF,QACA,EAAE,EACF/H,WACAC;4BAGJ,MAAMiI,YAAY,MAAML,gBACtBG,qBACAvK,UACAwH;4BAGF,uBAAuB;4BACvB,uGAAuG;4BACvG,OAAO;gCACLF,MAAM;gCACN6B,QAAQzI;gCACR+J;4BACF;wBACF,OAAO;4BACL,mGAAmG;4BACnG,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,gCAAgC;oBAEhC,gDAAgD;oBAChD,sCAAsC;oBACtC,IAAI,CAACrC,eAAe;wBAClB,OAAO;oBACT;oBAEA,IAAI;wBACF2B,cAAcW,sBAAsBzC,UAAUT;oBAChD,EAAE,OAAOrC,KAAK;wBACZ,OAAOwE,8BAA8BxE;oBACvC;oBAEA,4CAA4C;oBAC5C,oFAAoF;oBACpF,0FAA0F;oBAE1F,MAAMwF,SAAmB,EAAE;oBAC3B,WAAW,MAAMoB,SAASK,gBAAiB;wBACzCzB,OAAOK,IAAI,CAACE,OAAO7J,IAAI,CAAC0K;oBAC1B;oBAEA,MAAMd,aAAaC,OAAOC,MAAM,CAACR,QAAQvI,QAAQ,CAAC;oBAElD,IAAI8F,oBAAoB;wBACtB,MAAMlI,WAAWJ,8BAA8BqL;wBAC/CjB,uBAAuB,MAAME,YAC3BlK,UACAwH,iBACA;4BAAEgB;wBAAoB;oBAE1B,OAAO;wBACLwB,uBAAuB,MAAME,YAC3Be,YACAzD,iBACA;4BAAEgB;wBAAoB;oBAE1B;gBACF;YACF,OAAO;gBACL,MAAM,qBAA6C,CAA7C,IAAIjF,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAI;gBACFwG,cACEA,eAAeW,sBAAsBzC,UAAUT;YACnD,EAAE,OAAOrC,KAAK;gBACZ,OAAOwE,8BAA8BxE;YACvC;YAEA,MAAM+H,YAAa,MAAM3F,aAAa4F,YAAY,CAAC9B,OAAO,CACxDtB;YAEF,MAAMqD,gBACJF,SAAS,CACP,yFAAyF;YACzFjF,SACD;YAEH,MAAMoF,YAAY,MAAM7C,iCACtB4C,eACApD,sBACAzH,WACAC,cACA8K,OAAO,CAAC;gBACRhL,sBAAsBtB,KAAK;oBAAEuB;oBAAWC;gBAAa;YACvD;YAEA,4DAA4D;YAC5D,IAAI4F,eAAe;gBACjB,MAAMgB,eAAe,MAAM3B,eAAe1G,KAAK4G,KAAKnF,cAAc;oBAChE4G,cAAcH,QAAQsE,OAAO,CAACF;oBAC9B,iIAAiI;oBACjIhE,YAAY,CAAC9G,UAAUiL,kBAAkB,IAAIjE;oBAC7Cf;gBACF;gBAEA,OAAO;oBACLlB,MAAM;oBACN6B,QAAQC;gBACV;YACF,OAAO;gBACL,mFAAmF;gBACnF,mDAAmD;gBACnD,OAAO;YACT;QACF;IAEJ,EAAE,OAAOjE,KAAK;QACZ,IAAIsI,IAAAA,8BAAe,EAACtI,MAAM;YACxB,MAAMM,cAAciI,IAAAA,iCAAuB,EAACvI;YAC5C,MAAMW,eAAe6H,IAAAA,kCAAwB,EAACxI;YAE9C,mFAAmF;YACnF,2FAA2F;YAC3FnE,IAAI+H,UAAU,GAAG6E,sCAAkB,CAACC,QAAQ;YAC5CjG,SAASmB,UAAU,GAAG6E,sCAAkB,CAACC,QAAQ;YAEjD,IAAIzF,eAAe;gBACjB,OAAO;oBACLd,MAAM;oBACN6B,QAAQ,MAAMvD,2BACZ7E,KACAC,KACAmC,MACAsC,aACAK,cACA6B,IAAIK,UAAU,CAAC3E,QAAQ,EACvBd;gBAEJ;YACF;YAEA,+EAA+E;YAC/EvB,IAAI+B,SAAS,CAAC,YAAY0C;YAC1B,OAAO;gBACL6B,MAAM;gBACN6B,QAAQ7D,qBAAY,CAACC,UAAU,CAAC;YAClC;QACF,OAAO,IAAIuI,IAAAA,6CAAyB,EAAC3I,MAAM;YACzCnE,IAAI+H,UAAU,GAAGgF,IAAAA,+CAA2B,EAAC5I;YAC7CyC,SAASmB,UAAU,GAAG/H,IAAI+H,UAAU;YAEpC,IAAIX,eAAe;gBACjB,MAAMY,UAAUC,QAAQC,MAAM,CAAC/D;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAM6D;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBACA,OAAO;oBACL1B,MAAM;oBACN6B,QAAQ,MAAM1B,eAAe1G,KAAK4G,KAAKnF,cAAc;wBACnD6G,YAAY;wBACZD,cAAcJ;wBACdR;oBACF;gBACF;YACF;YAEA,yFAAyF;YACzF,OAAO;gBACLlB,MAAM;YACR;QACF;QAEA,4FAA4F;QAC5F,4CAA4C;QAE5C,IAAIc,eAAe;YACjB,+EAA+E;YAC/E,+EAA+E;YAC/E,oHAAoH;YACpHpH,IAAI+H,UAAU,GAAG;YACjBnB,SAASmB,UAAU,GAAG;YACtB,MAAMC,UAAUC,QAAQC,MAAM,CAAC/D;YAC/B,IAAI;gBACF,8DAA8D;gBAC9D,mDAAmD;gBACnD,yDAAyD;gBACzD,2CAA2C;gBAC3C,MAAM6D;YACR,EAAE,OAAM;YACN,qDAAqD;YACvD;YAEA,OAAO;gBACL1B,MAAM;gBACN6B,QAAQ,MAAM1B,eAAe1G,KAAK4G,KAAKnF,cAAc;oBACnD4G,cAAcJ;oBACd,iIAAiI;oBACjIK,YAAY,CAAC9G,UAAUiL,kBAAkB,IAAIjE;oBAC7Cf;gBACF;YACF;QACF;QAEA,6GAA6G;QAC7G,MAAMrD;IACR;AACF;AAEA,eAAeqF,iCAGbF,MAAW,EACX0D,IAAqB,EACrBzL,SAAoB,EACpBC,YAA0B;IAE1BA,aAAayL,KAAK,GAAG;IACrB,IAAI;QACF,OAAO,MAAMC,kDAAoB,CAACrE,GAAG,CAACrH,cAAc,IAClD8H,OAAO6D,KAAK,CAAC,MAAMH;IAEvB,SAAU;QACRxL,aAAayL,KAAK,GAAG;QAErB,4DAA4D;QAC5D,8EAA8E;QAC9E,mFAAmF;QACnF,qEAAqE;QACrEG,IAAAA,uCAAyB,EAAC5L;QAE1B,yEAAyE;QACzE,oEAAoE;QACpED,UAAU8L,WAAW,GAAG7L,aAAa8L,SAAS,CAACC,SAAS;QAExD,gHAAgH;QAChH,oEAAoE;QACpE,MAAMC,IAAAA,qCAAkB,EAACjM;IAC3B;AACF;AAEA;;;;CAIC,GACD,SAASmI,sBACPzC,QAAuB,EACvBT,eAAgC;QAOZA;IALpB,4EAA4E;IAC5E,IAAI,CAACS,UAAU;QACb,MAAM,qBAAmD,CAAnD,IAAIwG,8BAAc,CAAC,kCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,MAAM1E,eAAcvC,4BAAAA,eAAe,CAACS,SAAS,qBAAzBT,0BAA2BkH,EAAE;IAEjD,IAAI,CAAC3E,aAAa;QAChB,MAAM,qBAEL,CAFK,IAAIxG,MACR,CAAC,8BAA8B,EAAE0E,SAAS,qIAAqI,CAAC,GAD5K,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO8B;AACT", "ignoreList": [0]}