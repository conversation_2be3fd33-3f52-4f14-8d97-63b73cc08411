{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "sourcesContent": ["import path from 'path'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { nodeFs } from '../../server/lib/node-fs-methods'\nimport { interopDefault } from '../../lib/interop-default'\nimport { formatDynamicImportPath } from '../../lib/format-dynamic-import-path'\nimport {\n  initializeCacheHandlers,\n  setCacheHandler,\n} from '../../server/use-cache/handlers'\n\nexport async function createIncrementalCache({\n  cacheHandler,\n  cacheMaxMemorySize,\n  fetchCacheKeyPrefix,\n  distDir,\n  dir,\n  flushToDisk,\n  cacheHandlers,\n  requestHeaders,\n}: {\n  cacheHandler?: string\n  cacheMaxMemorySize?: number\n  fetchCacheKeyPrefix?: string\n  distDir: string\n  dir: string\n  flushToDisk?: boolean\n  requestHeaders?: Record<string, string | string[] | undefined>\n  cacheHandlers?: Record<string, string | undefined>\n}) {\n  // Custom cache handler overrides.\n  let CacheHandler: any\n  if (cacheHandler) {\n    CacheHandler = interopDefault(\n      await import(formatDynamicImportPath(dir, cacheHandler)).then(\n        (mod) => mod.default || mod\n      )\n    )\n  }\n\n  if (cacheHandlers && initializeCacheHandlers()) {\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await import(formatDynamicImportPath(dir, handler)).then(\n            (mod) => mod.default || mod\n          )\n        )\n      )\n    }\n  }\n\n  const incrementalCache = new IncrementalCache({\n    dev: false,\n    requestHeaders: requestHeaders || {},\n    flushToDisk,\n    maxMemoryCacheSize: cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    getPrerenderManifest: () => ({\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    }),\n    fs: nodeFs,\n    serverDistDir: path.join(distDir, 'server'),\n    CurCacheHandler: CacheHandler,\n    minimalMode: hasNextSupport,\n  })\n\n  ;(globalThis as any).__incrementalCache = incrementalCache\n\n  return incrementalCache\n}\n"], "names": ["createIncrementalCache", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "distDir", "dir", "flushToDisk", "cacheHandlers", "requestHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "then", "mod", "default", "initializeCacheHandlers", "kind", "handler", "Object", "entries", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incrementalCache", "IncrementalCache", "dev", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "nodeFs", "serverDistDir", "path", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "hasNextSupport", "globalThis", "__incrementalCache"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;6DAXL;kCACgB;wBACF;+BACR;gCACQ;yCACS;0BAIjC;;;;;;AAEA,eAAeA,uBAAuB,EAC3CC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,WAAW,EACXC,aAAa,EACbC,cAAc,EAUf;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,cAAc;QAChBQ,eAAeC,IAAAA,8BAAc,EAC3B,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACN,KAAKJ,eAAeW,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,IAAIN,iBAAiBQ,IAAAA,iCAAuB,KAAI;QAC9C,KAAK,MAAM,CAACC,MAAMC,QAAQ,IAAIC,OAAOC,OAAO,CAACZ,eAAgB;YAC3D,IAAI,CAACU,SAAS;YAEdG,IAAAA,yBAAe,EACbJ,MACAN,IAAAA,8BAAc,EACZ,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACN,KAAKY,UAAUL,IAAI,CACtD,CAACC,MAAQA,IAAIC,OAAO,IAAID;QAIhC;IACF;IAEA,MAAMQ,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5CC,KAAK;QACLf,gBAAgBA,kBAAkB,CAAC;QACnCF;QACAkB,oBAAoBtB;QACpBC;QACAsB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAIC,qBAAM;QACVC,eAAeC,aAAI,CAACC,IAAI,CAAClC,SAAS;QAClCmC,iBAAiB9B;QACjB+B,aAAaC,sBAAc;IAC7B;IAEEC,WAAmBC,kBAAkB,GAAGtB;IAE1C,OAAOA;AACT", "ignoreList": [0]}