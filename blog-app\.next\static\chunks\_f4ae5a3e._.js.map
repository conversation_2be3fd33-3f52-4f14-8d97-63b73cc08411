{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\n// 合并Tailwind CSS类名的工具函数\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const target = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return '刚刚'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}分钟前`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}小时前`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}天前`\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks}周前`\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths}个月前`\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears}年前`\n}\n\n// 生成文章摘要\nexport function generateExcerpt(content: string, maxLength: number = 150): string {\n  // 移除Markdown语法\n  const plainText = content\n    .replace(/#{1,6}\\s+/g, '') // 移除标题\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体\n    .replace(/`(.*?)`/g, '$1') // 移除行内代码\n    .replace(/```[\\s\\S]*?```/g, '') // 移除代码块\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // 移除链接，保留文本\n    .replace(/!\\[([^\\]]*)\\]\\([^)]+\\)/g, '') // 移除图片\n    .replace(/\\n+/g, ' ') // 替换换行为空格\n    .trim()\n\n  if (plainText.length <= maxLength) {\n    return plainText\n  }\n\n  return plainText.substring(0, maxLength).trim() + '...'\n}\n\n// 生成URL友好的slug\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .trim()\n    .replace(/[\\s\\W-]+/g, '-') // 替换空格和特殊字符为连字符\n    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function validatePassword(password: string): {\n  isValid: boolean\n  errors: string[]\n} {\n  const errors: string[] = []\n\n  if (password.length < 6) {\n    errors.push('密码至少需要6个字符')\n  }\n\n  if (!/[A-Za-z]/.test(password)) {\n    errors.push('密码需要包含字母')\n  }\n\n  if (!/\\d/.test(password)) {\n    errors.push('密码需要包含数字')\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  }\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n\n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n\n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean = false\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => {\n        inThrottle = false\n      }, limit)\n    }\n  }\n}\n\n// 复制到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('复制失败:', error)\n    return false\n  }\n}\n\n// 文件大小格式化\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// 随机生成ID\nexport function generateId(length: number = 8): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let result = ''\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  return result\n}\n\n// 安全的JSON解析\nexport function safeJsonParse<T>(str: string, fallback: T): T {\n  try {\n    return JSON.parse(str)\n  } catch {\n    return fallback\n  }\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// 平滑滚动到元素\nexport function scrollToElement(elementId: string, offset: number = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({\n      top,\n      behavior: 'smooth',\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO,EAAE,IAAI;IAEtE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,AAAC,GAAgB,OAAd,eAAc;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,AAAC,GAAe,OAAb,cAAa;IACzB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,AAAC,GAAc,OAAZ,aAAY;AACxB;AAGO,SAAS,gBAAgB,OAAe;QAAE,YAAA,iEAAoB;IACnE,eAAe;IACf,MAAM,YAAY,QACf,OAAO,CAAC,cAAc,IAAI,OAAO;KACjC,OAAO,CAAC,kBAAkB,MAAM,OAAO;KACvC,OAAO,CAAC,cAAc,MAAM,OAAO;KACnC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,mBAAmB,IAAI,QAAQ;KACvC,OAAO,CAAC,0BAA0B,MAAM,YAAY;KACpD,OAAO,CAAC,2BAA2B,IAAI,OAAO;KAC9C,OAAO,CAAC,QAAQ,KAAK,UAAU;KAC/B,IAAI;IAEP,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IAEA,OAAO,UAAU,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AACpD;AAGO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,IAAI,GACJ,OAAO,CAAC,aAAa,KAAK,gBAAgB;KAC1C,OAAO,CAAC,YAAY,IAAI,cAAc;;AAC3C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO;yCAAI;YAAA;;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI,aAAsB;IAE1B,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO;IACT;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS;QAAW,SAAA,iEAAiB;IAC1C,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,cAAiB,GAAW,EAAE,QAAW;IACvD,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS;IACd;;IACA,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB;QAAE,SAAA,iEAAiB;IAClE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YACd;YACA,UAAU;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/components/ui/Button.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = cn(\n      // 基础样式\n      'inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200',\n      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-apple-blue',\n      'disabled:opacity-50 disabled:cursor-not-allowed',\n      'active:scale-[0.98] transform',\n      \n      // 尺寸变体\n      {\n        'px-3 py-2 text-sm h-9': size === 'sm',\n        'px-4 py-2.5 text-base h-11': size === 'md',\n        'px-6 py-3 text-lg h-12': size === 'lg',\n      },\n      \n      // 颜色变体\n      {\n        // Primary - 苹果蓝色\n        'bg-apple-blue text-white hover:bg-blue-600 shadow-apple': variant === 'primary',\n        \n        // Secondary - 灰色\n        'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700': variant === 'secondary',\n        \n        // Outline - 边框样式\n        'border-2 border-apple-blue text-apple-blue hover:bg-apple-blue hover:text-white': variant === 'outline',\n        \n        // Ghost - 透明背景\n        'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800': variant === 'ghost',\n        \n        // Destructive - 红色警告\n        'bg-apple-red text-white hover:bg-red-600 shadow-apple': variant === 'destructive',\n      },\n      \n      className\n    )\n\n    return (\n      <motion.button\n        ref={ref}\n        className={baseClasses}\n        disabled={disabled || loading}\n        whileHover={{ scale: disabled || loading ? 1 : 1.02 }}\n        whileTap={{ scale: disabled || loading ? 1 : 0.98 }}\n        {...(props as any)}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </motion.button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,uBAAS,4RAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAiG;QAAhG,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC7F,MAAM,cAAc,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACnB,OAAO;IACP,8FACA,6EACA,mDACA,iCAEA,OAAO;IACP;QACE,yBAAyB,SAAS;QAClC,8BAA8B,SAAS;QACvC,0BAA0B,SAAS;IACrC,GAEA,OAAO;IACP;QACE,iBAAiB;QACjB,2DAA2D,YAAY;QAEvE,iBAAiB;QACjB,0GAA0G,YAAY;QAEtH,iBAAiB;QACjB,mFAAmF,YAAY;QAE/F,eAAe;QACf,6EAA6E,YAAY;QAEzF,qBAAqB;QACrB,yDAAyD,YAAY;IACvE,GAEA;IAGF,qBACE,4TAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QACpD,UAAU;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QACjD,GAAI,KAAK;;YAET,yBACC,4TAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,4TAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,4TAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X, Search, PenTool, User, LogOut, Sun, Moon } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/Button'\n\nexport function Header() {\n  const router = useRouter()\n  const { user, signOut } = useAuth()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isDarkMode, setIsDarkMode] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/')\n  }\n\n  const toggleDarkMode = () => {\n    setIsDarkMode(!isDarkMode)\n    document.documentElement.classList.toggle('dark')\n  }\n\n  const navItems = [\n    { href: '/', label: '首页' },\n    { href: '/categories', label: '分类' },\n    { href: '/about', label: '关于' },\n  ]\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"sticky top-0 z-50 glass-effect border-b border-gray-200/20 dark:border-gray-800/20\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\"\n            >\n              <span className=\"text-white font-bold text-lg\">B</span>\n            </motion.div>\n            <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              博客\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 font-medium\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleDarkMode}\n              className=\"p-2\"\n            >\n              {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"p-2\"\n            >\n              <Search size={20} />\n            </Button>\n\n            {user ? (\n              <div className=\"flex items-center space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => router.push('/write')}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <PenTool size={16} />\n                  <span>写文章</span>\n                </Button>\n                \n                <div className=\"relative group\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"p-2\"\n                  >\n                    <User size={20} />\n                  </Button>\n                  \n                  <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-apple border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                    <div className=\"p-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                      >\n                        个人中心\n                      </Link>\n                      <button\n                        onClick={handleSignOut}\n                        className=\"w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center space-x-2\"\n                      >\n                        <LogOut size={16} />\n                        <span>退出登录</span>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => router.push('/auth/login')}\n                >\n                  登录\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => router.push('/auth/register')}\n                >\n                  注册\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"md:hidden p-2\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-t border-gray-200/20 dark:border-gray-800/20\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              \n              <div className=\"pt-4 border-t border-gray-200 dark:border-gray-700\">\n                {user ? (\n                  <div className=\"space-y-2\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"w-full justify-start\"\n                      onClick={() => {\n                        router.push('/write')\n                        setIsMenuOpen(false)\n                      }}\n                    >\n                      <PenTool size={16} className=\"mr-2\" />\n                      写文章\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start\"\n                      onClick={() => {\n                        router.push('/profile')\n                        setIsMenuOpen(false)\n                      }}\n                    >\n                      <User size={16} className=\"mr-2\" />\n                      个人中心\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start text-red-600\"\n                      onClick={handleSignOut}\n                    >\n                      <LogOut size={16} className=\"mr-2\" />\n                      退出登录\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"w-full\"\n                      onClick={() => {\n                        router.push('/auth/login')\n                        setIsMenuOpen(false)\n                      }}\n                    >\n                      登录\n                    </Button>\n                    <Button\n                      className=\"w-full\"\n                      onClick={() => {\n                        router.push('/auth/register')\n                        setIsMenuOpen(false)\n                      }}\n                    >\n                      注册\n                    </Button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,cAAc,CAAC;QACf,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;IAC5C;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAK;QACzB;YAAE,MAAM;YAAe,OAAO;QAAK;QACnC;YAAE,MAAM;YAAU,OAAO;QAAK;KAC/B;IAED,qBACE,4TAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAEV,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,8RAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,4TAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,4TAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,4TAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAMpE,4TAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,4TAAC,8RAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,2BAAa,4TAAC,uRAAA,CAAA,MAAG;wCAAC,MAAM;;;;;6DAAS,4TAAC,yRAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGhD,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,4TAAC,6RAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;gCAGf,qBACC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,4TAAC,mSAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;8DACf,4TAAC;8DAAK;;;;;;;;;;;;sDAGR,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAGd,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,8RAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;0EACX;;;;;;0EAGD,4TAAC;gEACC,SAAS;gEACT,WAAU;;kFAEV,4TAAC,iSAAA,CAAA,SAAM;wEAAC,MAAM;;;;;;kFACd,4TAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOhB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;;;;;;;sCAQP,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BAAa,4TAAC,mRAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,4TAAC,yRAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMlD,4TAAC,kSAAA,CAAA,kBAAe;0BACb,4BACC,4TAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,4TAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,4TAAC,8RAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CASlB,4TAAC;gCAAI,WAAU;0CACZ,qBACC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,OAAO,IAAI,CAAC;gDACZ,cAAc;4CAChB;;8DAEA,4TAAC,mSAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAS;;;;;;;sDAGxC,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,OAAO,IAAI,CAAC;gDACZ,cAAc;4CAChB;;8DAEA,4TAAC,yRAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAS;;;;;;;sDAGrC,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;;8DAET,4TAAC,iSAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAS;;;;;;;;;;;;yDAKzC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,OAAO,IAAI,CAAC;gDACZ,cAAc;4CAChB;sDACD;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAS;gDACP,OAAO,IAAI,CAAC;gDACZ,cAAc;4CAChB;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA1OgB;;QACC,oQAAA,CAAA,YAAS;QACE,2HAAA,CAAA,UAAO;;;KAFnB", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/components/ui/Card.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'glass' | 'elevated'\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n  children: React.ReactNode\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', padding = 'md', children, ...props }, ref) => {\n    const cardClasses = cn(\n      // 基础样式\n      'rounded-2xl transition-all duration-300',\n      \n      // 变体样式\n      {\n        // Default - 标准卡片\n        'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-apple hover:shadow-apple-lg': variant === 'default',\n        \n        // Glass - 毛玻璃效果\n        'glass-effect': variant === 'glass',\n        \n        // Elevated - 悬浮效果\n        'bg-white dark:bg-gray-900 shadow-apple-lg hover:shadow-apple-xl transform hover:-translate-y-1': variant === 'elevated',\n      },\n      \n      // 内边距\n      {\n        'p-0': padding === 'none',\n        'p-4': padding === 'sm',\n        'p-6': padding === 'md',\n        'p-8': padding === 'lg',\n      },\n      \n      className\n    )\n\n    return (\n      <motion.div\n        ref={ref}\n        className={cardClasses}\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n        whileHover={{ scale: variant === 'elevated' ? 1.02 : 1 }}\n        {...(props as any)}\n      >\n        {children}\n      </motion.div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\n// Card子组件\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 pb-4', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-gray-600 dark:text-gray-400', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center pt-4', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,qBAAO,4RAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAyE;QAAxE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IACrE,MAAM,cAAc,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACnB,OAAO;IACP,2CAEA,OAAO;IACP;QACE,iBAAiB;QACjB,4GAA4G,YAAY;QAExH,gBAAgB;QAChB,gBAAgB,YAAY;QAE5B,kBAAkB;QAClB,kGAAkG,YAAY;IAChH,GAEA,MAAM;IACN;QACE,OAAO,YAAY;QACnB,OAAO,YAAY;QACnB,OAAO,YAAY;QACnB,OAAO,YAAY;IACrB,GAEA;IAGF,qBACE,4TAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,OAAO,YAAY,aAAa,OAAO;QAAE;QACtD,GAAI,KAAK;kBAET;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAEnB,UAAU;AACV,MAAM,2BAAa,4RAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;;;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4RAAA,CAAA,UAAK,CAAC,UAAU,OAChC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sFAAsF;QACnG,GAAG,KAAK;;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4RAAA,CAAA,UAAK,CAAC,UAAU,OACtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4RAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;;;AAIf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4RAAA,CAAA,UAAK,CAAC,UAAU,QACjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/developer/metting/manager/blog-app/src/app/post/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useParams } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Calendar, Clock, Heart, MessageCircle, Share2, Bookmark, ArrowLeft, User } from 'lucide-react'\nimport { Header } from '@/components/layout/Header'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card'\n\n// 模拟文章数据\nconst mockPost = {\n  id: '1',\n  title: 'React 18 新特性深度解析',\n  content: `# React 18 新特性深度解析\n\nReact 18 是 React 的一个重要版本，引入了许多令人兴奋的新特性和改进。在这篇文章中，我们将深入探讨这些新特性，并了解如何在实际项目中应用它们。\n\n## 并发特性 (Concurrent Features)\n\nReact 18 最重要的更新之一就是并发特性的引入。这些特性允许 React 在渲染过程中暂停、恢复或放弃工作，从而提供更好的用户体验。\n\n### Automatic Batching\n\n在 React 18 之前，React 只会在事件处理器中批量更新状态。现在，React 会自动批量处理所有状态更新，包括在 Promise、setTimeout 和原生事件处理器中的更新。\n\n\\`\\`\\`javascript\n// React 18 之前，这些更新不会被批量处理\nsetTimeout(() => {\n  setCount(c => c + 1);\n  setFlag(f => !f);\n  // React 会渲染两次，每次状态更新一次\n}, 1000);\n\n// React 18 中，这些更新会被自动批量处理\nsetTimeout(() => {\n  setCount(c => c + 1);\n  setFlag(f => !f);\n  // React 只会渲染一次\n}, 1000);\n\\`\\`\\`\n\n### Suspense 改进\n\nReact 18 对 Suspense 进行了重大改进，现在支持服务端渲染，并且提供了更好的错误边界处理。\n\n\\`\\`\\`jsx\nfunction App() {\n  return (\n    <Suspense fallback={<Loading />}>\n      <ComponentThatSuspendsOnData />\n      <Sibling />\n    </Suspense>\n  );\n}\n\\`\\`\\`\n\n## 新的 Hooks\n\nReact 18 还引入了几个新的 Hooks，让开发者能够更好地利用并发特性。\n\n### useId\n\n\\`useId\\` Hook 用于生成唯一的 ID，特别适用于可访问性属性。\n\n\\`\\`\\`javascript\nfunction Checkbox() {\n  const id = useId();\n  return (\n    <>\n      <label htmlFor={id}>选择我</label>\n      <input id={id} type=\"checkbox\" name=\"checkbox\"/>\n    </>\n  );\n}\n\\`\\`\\`\n\n### useTransition\n\n\\`useTransition\\` 允许你将状态更新标记为非紧急的，让 React 知道可以中断这些更新来处理更重要的任务。\n\n\\`\\`\\`javascript\nfunction App() {\n  const [isPending, startTransition] = useTransition();\n  const [count, setCount] = useState(0);\n  \n  function handleClick() {\n    startTransition(() => {\n      setCount(c => c + 1);\n    });\n  }\n\n  return (\n    <div>\n      {isPending && <Spinner />}\n      <button onClick={handleClick}>{count}</button>\n    </div>\n  );\n}\n\\`\\`\\`\n\n## 总结\n\nReact 18 带来的这些新特性为构建更好的用户体验提供了强大的工具。并发特性让应用更加响应，新的 Hooks 提供了更多的控制能力，而 Suspense 的改进则让数据获取变得更加优雅。\n\n在升级到 React 18 时，大多数应用都能够平滑过渡，因为这些新特性都是可选的。但是，了解和掌握这些特性将帮助你构建更好的 React 应用。`,\n  excerpt: '探索 React 18 带来的并发特性、自动批处理、Suspense 改进等重要更新，以及如何在项目中应用这些新特性。',\n  author: {\n    id: '1',\n    name: '张三',\n    avatar: '/avatars/avatar1.jpg',\n    bio: '前端开发工程师，专注于 React 生态系统'\n  },\n  category: '技术',\n  tags: ['React', 'JavaScript', '前端开发'],\n  publishedAt: '2024-01-15',\n  updatedAt: '2024-01-15',\n  readTime: 8,\n  likes: 42,\n  comments: 12,\n  isLiked: false,\n  isBookmarked: false\n}\n\nexport default function PostPage() {\n  const params = useParams()\n  const [post, setPost] = useState(mockPost)\n  const [isLiked, setIsLiked] = useState(post.isLiked)\n  const [isBookmarked, setIsBookmarked] = useState(post.isBookmarked)\n  const [likesCount, setLikesCount] = useState(post.likes)\n\n  const handleLike = () => {\n    setIsLiked(!isLiked)\n    setLikesCount(prev => isLiked ? prev - 1 : prev + 1)\n  }\n\n  const handleBookmark = () => {\n    setIsBookmarked(!isBookmarked)\n  }\n\n  const handleShare = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: post.title,\n          text: post.excerpt,\n          url: window.location.href,\n        })\n      } catch (error) {\n        console.log('分享失败:', error)\n      }\n    } else {\n      // 复制链接到剪贴板\n      navigator.clipboard.writeText(window.location.href)\n      alert('链接已复制到剪贴板')\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Header />\n      \n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* 返回按钮 */}\n          <Button\n            variant=\"ghost\"\n            onClick={() => window.history.back()}\n            className=\"mb-6\"\n          >\n            <ArrowLeft size={16} className=\"mr-2\" />\n            返回\n          </Button>\n\n          {/* 文章头部 */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                {post.category}\n              </span>\n              {post.tags.map((tag) => (\n                <span\n                  key={tag}\n                  className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200\"\n                >\n                  #{tag}\n                </span>\n              ))}\n            </div>\n\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              {post.title}\n            </h1>\n\n            <p className=\"text-xl text-gray-600 dark:text-gray-400 mb-6\">\n              {post.excerpt}\n            </p>\n\n            {/* 作者信息和元数据 */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                    <User className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-medium text-gray-900 dark:text-white\">\n                      {post.author.name}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {post.author.bio}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400\">\n                <div className=\"flex items-center\">\n                  <Calendar size={16} className=\"mr-1\" />\n                  {post.publishedAt}\n                </div>\n                <div className=\"flex items-center\">\n                  <Clock size={16} className=\"mr-1\" />\n                  {post.readTime} 分钟阅读\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 文章内容 */}\n          <Card className=\"mb-8\">\n            <CardContent className=\"p-8\">\n              <div className=\"prose prose-lg dark:prose-invert max-w-none\">\n                <div className=\"whitespace-pre-wrap\">\n                  {post.content}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 互动按钮 */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant={isLiked ? 'primary' : 'outline'}\n                onClick={handleLike}\n                className=\"flex items-center space-x-2\"\n              >\n                <Heart size={16} className={isLiked ? 'fill-current' : ''} />\n                <span>{likesCount}</span>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center space-x-2\"\n              >\n                <MessageCircle size={16} />\n                <span>{post.comments}</span>\n              </Button>\n\n              <Button\n                variant={isBookmarked ? 'primary' : 'outline'}\n                onClick={handleBookmark}\n              >\n                <Bookmark size={16} className={isBookmarked ? 'fill-current' : ''} />\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                onClick={handleShare}\n              >\n                <Share2 size={16} />\n              </Button>\n            </div>\n          </div>\n\n          {/* 作者卡片 */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                  <User className=\"w-8 h-8 text-white\" />\n                </div>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {post.author.name}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400\">\n                    {post.author.bio}\n                  </p>\n                </div>\n                <Button variant=\"outline\">\n                  关注\n                </Button>\n              </div>\n            </CardHeader>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,SAAS;AACT,MAAM,WAAW;IACf,IAAI;IACJ,OAAO;IACP,SAAU;IA4FV,SAAS;IACT,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,KAAK;IACP;IACA,UAAU;IACV,MAAM;QAAC;QAAS;QAAc;KAAO;IACrC,aAAa;IACb,WAAW;IACX,UAAU;IACV,OAAO;IACP,UAAU;IACV,SAAS;IACT,cAAc;AAChB;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,YAAY;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IAEvD,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ,cAAc,CAAA,OAAQ,UAAU,OAAO,IAAI,OAAO;IACpD;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,OAAO;oBAClB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,SAAS;YACvB;QACF,OAAO;YACL,WAAW;YACX,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,MAAM;QACR;IACF;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,kIAAA,CAAA,SAAM;;;;;0BAEP,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAG5B,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;4BAClC,WAAU;;8CAEV,4TAAC,uSAAA,CAAA,YAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;sCAK1C,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAK,WAAU;sDACb,KAAK,QAAQ;;;;;;wCAEf,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,4TAAC;gDAEC,WAAU;;oDACX;oDACG;;+CAHG;;;;;;;;;;;8CAQX,4TAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAGb,4TAAC;oCAAE,WAAU;8CACV,KAAK,OAAO;;;;;;8CAIf,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,4TAAC;;0EACC,4TAAC;gEAAI,WAAU;0EACZ,KAAK,MAAM,CAAC,IAAI;;;;;;0EAEnB,4TAAC;gEAAI,WAAU;0EACZ,KAAK,MAAM,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;sDAMxB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,iSAAA,CAAA,WAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAC7B,KAAK,WAAW;;;;;;;8DAEnB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,2RAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAC1B,KAAK,QAAQ;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvB,4TAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;sCAOrB,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,UAAU,YAAY;wCAC/B,SAAS;wCACT,WAAU;;0DAEV,4TAAC,2RAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAW,UAAU,iBAAiB;;;;;;0DACvD,4TAAC;0DAAM;;;;;;;;;;;;kDAGT,4TAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,+SAAA,CAAA,gBAAa;gDAAC,MAAM;;;;;;0DACrB,4TAAC;0DAAM,KAAK,QAAQ;;;;;;;;;;;;kDAGtB,4TAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,eAAe,YAAY;wCACpC,SAAS;kDAET,cAAA,4TAAC,iSAAA,CAAA,WAAQ;4CAAC,MAAM;4CAAI,WAAW,eAAe,iBAAiB;;;;;;;;;;;kDAGjE,4TAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;kDAET,cAAA,4TAAC,iSAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;sCAMpB,4TAAC,4HAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,4HAAA,CAAA,aAAU;0CACT,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAG,WAAU;8DACX,KAAK,MAAM,CAAC,IAAI;;;;;;8DAEnB,4TAAC;oDAAE,WAAU;8DACV,KAAK,MAAM,CAAC,GAAG;;;;;;;;;;;;sDAGpB,4TAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C;GArLwB;;QACP,oQAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}