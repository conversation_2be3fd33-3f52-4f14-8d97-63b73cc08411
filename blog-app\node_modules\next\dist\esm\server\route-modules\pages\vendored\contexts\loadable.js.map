{"version": 3, "sources": ["../../../../../../src/server/route-modules/pages/vendored/contexts/loadable.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].Loadable\n"], "names": ["module", "exports", "require", "vendored", "Loadable"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,QAAQ", "ignoreList": [0]}