{"version": 3, "sources": ["../../../src/lib/helpers/get-npx-command.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport { getPkgManager } from './get-pkg-manager'\n\nexport function getNpxCommand(baseDir: string) {\n  const pkgManager = getPkgManager(baseDir)\n  let command = 'npx'\n  if (pkgManager === 'pnpm') {\n    command = 'pnpm dlx'\n  } else if (pkgManager === 'yarn') {\n    try {\n      execSync('yarn dlx --help', { stdio: 'ignore' })\n      command = 'yarn dlx'\n    } catch {}\n  }\n\n  return command\n}\n"], "names": ["getNpxCommand", "baseDir", "pkgManager", "getPkgManager", "command", "execSync", "stdio"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;+BAHS;+BACK;AAEvB,SAASA,cAAcC,OAAe;IAC3C,MAAMC,aAAaC,IAAAA,4BAAa,EAACF;IACjC,IAAIG,UAAU;IACd,IAAIF,eAAe,QAAQ;QACzBE,UAAU;IACZ,OAAO,IAAIF,eAAe,QAAQ;QAChC,IAAI;YACFG,IAAAA,uBAAQ,EAAC,mBAAmB;gBAAEC,OAAO;YAAS;YAC9CF,UAAU;QACZ,EAAE,OAAM,CAAC;IACX;IAEA,OAAOA;AACT", "ignoreList": [0]}