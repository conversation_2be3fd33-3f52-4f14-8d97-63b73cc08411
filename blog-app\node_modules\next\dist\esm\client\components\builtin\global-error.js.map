{"version": 3, "sources": ["../../../../src/client/components/builtin/global-error.tsx"], "sourcesContent": ["'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n"], "names": ["HandleISRError", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "window", "location", "hostname", "p"], "mappings": "AAAA;;AAEA,SAASA,cAAc,QAAQ,sBAAqB;AAEpD,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AAKA,SAASC,mBAAmB,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IAC1B,MAAMc,SAA6Bd,yBAAAA,MAAOc,MAAM;IAChD,qBACE,MAACC;QAAKC,IAAG;;0BACP,KAACC;0BACD,MAACC;;kCACC,KAACpB;wBAAeE,OAAOA;;kCACvB,KAACmB;wBAAIC,OAAOrB,OAAOC,KAAK;kCACtB,cAAA,MAACmB;;8CACC,MAACE;oCAAGD,OAAOrB,OAAOS,IAAI;;wCAAE;wCACAM,SAAS,WAAW;wCAAS;wCACvBQ,OAAOC,QAAQ,CAACC,QAAQ;wCAAC;wCAAU;wCAC9DV,SAAS,gBAAgB;wCAAkB;;;gCAG7CA,uBAAS,KAACW;oCAAEL,OAAOrB,OAAOS,IAAI;8CAAG,AAAC,aAAUM;qCAAgB;;;;;;;;AAMzE;AAEA,gFAAgF;AAChF,2CAA2C;AAC3C,eAAeD,mBAAkB", "ignoreList": [0]}