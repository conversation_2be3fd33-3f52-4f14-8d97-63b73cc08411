{"version": 3, "sources": ["../../src/lib/error-telemetry-utils.ts"], "sourcesContent": ["const ERROR_CODE_DELIMITER = '@'\n\n/**\n * Augments the digest field of errors thrown in React Server Components (RSC) with an error code.\n * Since RSC errors can only be serialized through the digest field, this provides a way to include\n * an additional error code that can be extracted client-side via `extractNextErrorCode`.\n *\n * The error code is appended to the digest string with a semicolon separator, allowing it to be\n * parsed out later while preserving the original digest value.\n */\nexport const createDigestWithErrorCode = (\n  thrownValue: unknown,\n  originalDigest: string\n): string => {\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    '__NEXT_ERROR_CODE' in thrownValue\n  ) {\n    return `${originalDigest}${ERROR_CODE_DELIMITER}${thrownValue.__NEXT_ERROR_CODE}`\n  }\n  return originalDigest\n}\n\nexport const extractNextErrorCode = (error: unknown): string | undefined => {\n  if (\n    typeof error === 'object' &&\n    error !== null &&\n    '__NEXT_ERROR_CODE' in error &&\n    typeof error.__NEXT_ERROR_CODE === 'string'\n  ) {\n    return error.__NEXT_ERROR_CODE\n  }\n\n  if (\n    typeof error === 'object' &&\n    error !== null &&\n    'digest' in error &&\n    typeof error.digest === 'string'\n  ) {\n    const segments = error.digest.split(ERROR_CODE_DELIMITER)\n    const errorCode = segments.find((segment) => segment.startsWith('E'))\n    return errorCode\n  }\n\n  return undefined\n}\n"], "names": ["ERROR_CODE_DELIMITER", "createDigestWithErrorCode", "thrownValue", "originalDigest", "__NEXT_ERROR_CODE", "extractNextErrorCode", "error", "digest", "segments", "split", "errorCode", "find", "segment", "startsWith", "undefined"], "mappings": "AAAA,MAAMA,uBAAuB;AAE7B;;;;;;;CAOC,GACD,OAAO,MAAMC,4BAA4B,CACvCC,aACAC;IAEA,IACE,OAAOD,gBAAgB,YACvBA,gBAAgB,QAChB,uBAAuBA,aACvB;QACA,OAAO,GAAGC,iBAAiBH,uBAAuBE,YAAYE,iBAAiB,EAAE;IACnF;IACA,OAAOD;AACT,EAAC;AAED,OAAO,MAAME,uBAAuB,CAACC;IACnC,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,uBAAuBA,SACvB,OAAOA,MAAMF,iBAAiB,KAAK,UACnC;QACA,OAAOE,MAAMF,iBAAiB;IAChC;IAEA,IACE,OAAOE,UAAU,YACjBA,UAAU,QACV,YAAYA,SACZ,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,MAAMC,WAAWF,MAAMC,MAAM,CAACE,KAAK,CAACT;QACpC,MAAMU,YAAYF,SAASG,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC;QAChE,OAAOH;IACT;IAEA,OAAOI;AACT,EAAC", "ignoreList": [0]}