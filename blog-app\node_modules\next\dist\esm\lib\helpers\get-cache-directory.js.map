{"version": 3, "sources": ["../../../src/lib/helpers/get-cache-directory.ts"], "sourcesContent": ["import os from 'os'\nimport path from 'path'\nimport fs from 'fs'\n\n// get platform specific cache directory adapted from playwright's handling\n// https://github.com/microsoft/playwright/blob/7d924470d397975a74a19184c136b3573a974e13/packages/playwright-core/src/utils/registry.ts#L141\nexport function getCacheDirectory(fileDirectory: string, envPath?: string) {\n  let result\n\n  if (envPath) {\n    result = envPath\n  } else {\n    let systemCacheDirectory\n    if (process.platform === 'linux') {\n      systemCacheDirectory =\n        process.env.XDG_CACHE_HOME || path.join(os.homedir(), '.cache')\n    } else if (process.platform === 'darwin') {\n      systemCacheDirectory = path.join(os.homedir(), 'Library', 'Caches')\n    } else if (process.platform === 'win32') {\n      systemCacheDirectory =\n        process.env.LOCALAPPDATA || path.join(os.homedir(), 'AppData', 'Local')\n    } else {\n      /// Attempt to use generic tmp location for un-handled platform\n      if (!systemCacheDirectory) {\n        for (const dir of [\n          path.join(os.homedir(), '.cache'),\n          path.join(os.tmpdir()),\n        ]) {\n          if (fs.existsSync(dir)) {\n            systemCacheDirectory = dir\n            break\n          }\n        }\n      }\n\n      if (!systemCacheDirectory) {\n        console.error(new Error('Unsupported platform: ' + process.platform))\n        process.exit(0)\n      }\n    }\n    result = path.join(systemCacheDirectory, fileDirectory)\n  }\n\n  if (!path.isAbsolute(result)) {\n    // It is important to resolve to the absolute path:\n    //   - for unzipping to work correctly;\n    //   - so that registry directory matches between installation and execution.\n    // INIT_CWD points to the root of `npm/yarn install` and is probably what\n    // the user meant when typing the relative path.\n    result = path.resolve(process.env['INIT_CWD'] || process.cwd(), result)\n  }\n  return result\n}\n"], "names": ["os", "path", "fs", "getCacheDirectory", "fileDirectory", "envPath", "result", "systemCacheDirectory", "process", "platform", "env", "XDG_CACHE_HOME", "join", "homedir", "LOCALAPPDATA", "dir", "tmpdir", "existsSync", "console", "error", "Error", "exit", "isAbsolute", "resolve", "cwd"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,2EAA2E;AAC3E,4IAA4I;AAC5I,OAAO,SAASC,kBAAkBC,aAAqB,EAAEC,OAAgB;IACvE,IAAIC;IAEJ,IAAID,SAAS;QACXC,SAASD;IACX,OAAO;QACL,IAAIE;QACJ,IAAIC,QAAQC,QAAQ,KAAK,SAAS;YAChCF,uBACEC,QAAQE,GAAG,CAACC,cAAc,IAAIV,KAAKW,IAAI,CAACZ,GAAGa,OAAO,IAAI;QAC1D,OAAO,IAAIL,QAAQC,QAAQ,KAAK,UAAU;YACxCF,uBAAuBN,KAAKW,IAAI,CAACZ,GAAGa,OAAO,IAAI,WAAW;QAC5D,OAAO,IAAIL,QAAQC,QAAQ,KAAK,SAAS;YACvCF,uBACEC,QAAQE,GAAG,CAACI,YAAY,IAAIb,KAAKW,IAAI,CAACZ,GAAGa,OAAO,IAAI,WAAW;QACnE,OAAO;YACL,+DAA+D;YAC/D,IAAI,CAACN,sBAAsB;gBACzB,KAAK,MAAMQ,OAAO;oBAChBd,KAAKW,IAAI,CAACZ,GAAGa,OAAO,IAAI;oBACxBZ,KAAKW,IAAI,CAACZ,GAAGgB,MAAM;iBACpB,CAAE;oBACD,IAAId,GAAGe,UAAU,CAACF,MAAM;wBACtBR,uBAAuBQ;wBACvB;oBACF;gBACF;YACF;YAEA,IAAI,CAACR,sBAAsB;gBACzBW,QAAQC,KAAK,CAAC,qBAAsD,CAAtD,IAAIC,MAAM,2BAA2BZ,QAAQC,QAAQ,GAArD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAqD;gBACnED,QAAQa,IAAI,CAAC;YACf;QACF;QACAf,SAASL,KAAKW,IAAI,CAACL,sBAAsBH;IAC3C;IAEA,IAAI,CAACH,KAAKqB,UAAU,CAAChB,SAAS;QAC5B,mDAAmD;QACnD,uCAAuC;QACvC,6EAA6E;QAC7E,yEAAyE;QACzE,gDAAgD;QAChDA,SAASL,KAAKsB,OAAO,CAACf,QAAQE,GAAG,CAAC,WAAW,IAAIF,QAAQgB,GAAG,IAAIlB;IAClE;IACA,OAAOA;AACT", "ignoreList": [0]}