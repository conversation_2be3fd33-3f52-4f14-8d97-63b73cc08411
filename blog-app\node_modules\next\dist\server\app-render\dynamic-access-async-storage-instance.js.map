{"version": 3, "sources": ["../../../src/server/app-render/dynamic-access-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { DynamicAccessStorage } from './dynamic-access-async-storage.external'\n\nexport const dynamicAccessAsyncStorageInstance: DynamicAccessStorage =\n  createAsyncLocalStorage()\n"], "names": ["dynamicAccessAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,oCACXC,IAAAA,0CAAuB", "ignoreList": [0]}