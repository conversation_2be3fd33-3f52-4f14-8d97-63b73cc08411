{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/app-dev-overlay-setup.ts"], "sourcesContent": ["import { patchConsoleError } from './errors/intercept-console-error'\nimport { handleGlobalErrors } from './errors/use-error-handler'\nimport {\n  initializeDebugLogForwarding,\n  isTerminalLoggingEnabled,\n} from './forward-logs'\n\nhandleGlobalErrors()\npatchConsoleError()\n\nif (isTerminalLoggingEnabled) {\n  initializeDebugLogForwarding('app')\n}\n"], "names": ["handleGlobalErrors", "patchConsoleError", "isTerminalLoggingEnabled", "initializeDebugLogForwarding"], "mappings": ";;;;uCAAkC;iCACC;6BAI5B;AAEPA,IAAAA,mCAAkB;AAClBC,IAAAA,wCAAiB;AAEjB,IAAIC,qCAAwB,EAAE;IAC5BC,IAAAA,yCAA4B,EAAC;AAC/B", "ignoreList": [0]}