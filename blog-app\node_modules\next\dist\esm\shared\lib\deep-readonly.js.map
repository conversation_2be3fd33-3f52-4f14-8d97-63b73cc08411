{"version": 3, "sources": ["../../../src/shared/lib/deep-readonly.ts"], "sourcesContent": ["/**\n * A type that represents a deeply readonly object. This is similar to\n * TypeScript's `Readonly` type, but it recursively applies the `readonly`\n * modifier to all properties of an object and all elements of arrays.\n */\nexport type DeepReadonly<T> = T extends (infer R)[]\n  ? ReadonlyArray<DeepReadonly<R>>\n  : T extends object\n    ? {\n        readonly [K in keyof T]: DeepReadonly<T[K]>\n      }\n    : T\n"], "names": [], "mappings": "AAAA;;;;CAIC,GACD,WAMO", "ignoreList": [0]}