!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("@sinclair/typebox/compiler"),require("@sinclair/typebox/value"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","@sinclair/typebox/compiler","@sinclair/typebox/value","react-hook-form"],r):r((e||self).hookformResolversTypebox={},e.hookformResolvers,e.compiler,e.value,e.ReactHookForm)}(this,function(e,r,o,s,t){function i(e,r){for(var o={};e.length;){var s=e[0],i=s.type,a=s.message,l=s.path.substring(1).replace(/\//g,".");if(o[l]||(o[l]={message:a,type:""+i}),r){var n=o[l].types,f=n&&n[""+i];o[l]=t.appendErrors(l,r,o,""+i,f?[].concat(f,s.message):s.message)}e.shift()}return o}e.typeboxResolver=function(e){return function(t,a,l){try{var n=Array.from(e instanceof o.TypeCheck?e.Errors(t):s.Value.Errors(e,t));return l.shouldUseNativeValidation&&r.validateFieldsNatively({},l),Promise.resolve(n.length?{values:{},errors:r.toNestErrors(i(n,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)}:{errors:{},values:t})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=typebox.umd.js.map
