{"version": 3, "sources": ["../../../src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n          return makeHangingCookies(workUnitStore)\n        case 'prerender-client':\n          const exportName = '`cookies`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no dynamicIO)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how cookies has worked in PPR without dynamicIO.\n          postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n          break\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We track dynamic access here so we don't need to wrap the cookies in\n          // individual property access tracking.\n          throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n          break\n        default:\n        // fallthrough\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using cookies inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  // cookies is being called in a dynamic context\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  let underlyingCookies: ReadonlyRequestCookies\n\n  if (areCookiesMutableInCurrentPhase(requestStore)) {\n    // We can't conditionally return different types here based on the context.\n    // To avoid confusion, we always return the readonly type here.\n    underlyingCookies =\n      requestStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n  } else {\n    underlyingCookies = requestStore.cookies\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeUntrackedCookiesWithDevWarnings(\n        underlyingCookies,\n        workStore?.route\n      )\n    }\n\n    return makeUntrackedExoticCookiesWithDevWarnings(\n      underlyingCookies,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticCookies(underlyingCookies)\n  }\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeHangingCookies(\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticCookiesWithDevWarnings`, but just logging the\n// sync access without actually defining the cookies properties on the promise.\nfunction makeUntrackedCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...cookies()` or similar iteration')\n          break\n        }\n        case 'size':\n        case 'get':\n        case 'getAll':\n        case 'has':\n        case 'set':\n        case 'delete':\n        case 'clear':\n        case 'toString': {\n          warnForSyncAccess(route, `\\`cookies().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the cookies object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedCookies.set(underlyingCookies, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["areCookiesMutableInCurrentPhase", "RequestCookiesAdapter", "RequestCookies", "workAsyncStorage", "workUnitAsyncStorage", "postponeWithTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "getExpectedRequestStore", "StaticGenBailoutError", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "scheduleImmediate", "isRequestAPICallableInsideAfter", "InvariantError", "ReflectAdapter", "cookies", "callingExpression", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "type", "dynamicShouldError", "makeHangingCookies", "exportName", "dynamicTracking", "requestStore", "userspaceMutableCookies", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeUntrackedCookiesWithDevWarnings", "makeUntrackedExoticCookiesWithDevWarnings", "seal", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "renderSignal", "set", "cachedCookies", "Promise", "resolve", "Object", "defineProperties", "Symbol", "iterator", "value", "bind", "polyfilledResponseCookiesIterator", "size", "getAll", "has", "delete", "clear", "polyfilledResponseCookiesClear", "toString", "expression", "syncIODev", "apply", "arguments", "call", "writable", "length", "describeNameArg", "arg", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "name", "prerenderPhase", "createCookiesAccessError", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": "AAAA,SAGEA,+BAA+B,EAC/BC,qBAAqB,QAChB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,uBAAuB,QAAQ,iDAAgD;AACxF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,+BAA+B,QAAQ,UAAS;AACzD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,yCAAwC;AAyBvE,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYhB,iBAAiBiB,QAAQ;IAC3C,MAAMC,gBAAgBjB,qBAAqBgB,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACR,mCACD;YACA,MAAM,qBAGL,CAHK,IAAIS,MACR,wDAAwD;YACxD,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcQ,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIH,cAAcQ,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIL,UAAUW,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIpB,sBACR,CAAC,MAAM,EAAES,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,OAAQA,cAAcQ,IAAI;gBACxB,KAAK;oBACH,OAAOE,mBAAmBV;gBAC5B,KAAK;oBACH,MAAMW,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIjB,eACR,GAAGiB,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,+BAA+B;oBAC/B,0EAA0E;oBAC1E,2EAA2E;oBAC3E3B,qBACEc,UAAUK,KAAK,EACfN,mBACAG,cAAcY,eAAe;oBAE/B;gBACF,KAAK;oBACH,mBAAmB;oBACnB,uEAAuE;oBACvE,uCAAuC;oBACvC3B,iCACEY,mBACAC,WACAE;oBAEF;gBACF;YAEF;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFd,gCAAgCY,WAAWE;IAC7C;IAEA,+CAA+C;IAE/C,MAAMa,eAAezB,wBAAwBS;IAE7C,IAAIQ;IAEJ,IAAI1B,gCAAgCkC,eAAe;QACjD,2EAA2E;QAC3E,+DAA+D;QAC/DR,oBACEQ,aAAaC,uBAAuB;IACxC,OAAO;QACLT,oBAAoBQ,aAAajB,OAAO;IAC1C;IAEA,IAAImB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAACnB,6BAAAA,UAAWoB,iBAAiB,GAAE;QAC3E,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;YACjC,OAAOC,oCACLf,mBACAP,6BAAAA,UAAWK,KAAK;QAEpB;QAEA,OAAOkB,0CACLhB,mBACAP,6BAAAA,UAAWK,KAAK;IAEpB,OAAO;QACL,OAAOI,2BAA2BF;IACpC;AACF;AAEA,SAASC;IACP,OAAO1B,sBAAsB0C,IAAI,CAAC,IAAIzC,eAAe,IAAI0C,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASf,mBACPgB,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUvC,mBACdoC,eAAeI,YAAY,EAC3B;IAEFN,cAAcO,GAAG,CAACL,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAAStB,2BACPF,iBAAyC;IAEzC,MAAM2B,gBAAgBR,cAAcI,GAAG,CAACvB;IACxC,IAAI2B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAUI,QAAQC,OAAO,CAAC7B;IAChCmB,cAAcO,GAAG,CAAC1B,mBAAmBwB;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/B,CAACQ,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAOlC,iBAAiB,CAACgC,OAAOC,QAAQ,CAAC,GACrCjC,iBAAiB,CAACgC,OAAOC,QAAQ,CAAC,CAACE,IAAI,CAACnC,qBAExC,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjEoC,kCAAkCD,IAAI,CAACnC;QAC7C;QACAqC,MAAM;YACJd;gBACE,OAAOvB,kBAAkBqC,IAAI;YAC/B;QACF;QACAd,KAAK;YACHW,OAAOlC,kBAAkBuB,GAAG,CAACY,IAAI,CAACnC;QACpC;QACAsC,QAAQ;YACNJ,OAAOlC,kBAAkBsC,MAAM,CAACH,IAAI,CAACnC;QACvC;QACAuC,KAAK;YACHL,OAAOlC,kBAAkBuC,GAAG,CAACJ,IAAI,CAACnC;QACpC;QACA0B,KAAK;YACHQ,OAAOlC,kBAAkB0B,GAAG,CAACS,IAAI,CAACnC;QACpC;QACAwC,QAAQ;YACNN,OAAOlC,kBAAkBwC,MAAM,CAACL,IAAI,CAACnC;QACvC;QACAyC,OAAO;YACLP,OACE,yFAAyF;YACzF,OAAOlC,kBAAkByC,KAAK,KAAK,aAE/BzC,kBAAkByC,KAAK,CAACN,IAAI,CAACnC,qBAE7B,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjE0C,+BAA+BP,IAAI,CAACnC,mBAAmBwB;QAC/D;QACAmB,UAAU;YACRT,OAAOlC,kBAAkB2C,QAAQ,CAACR,IAAI,CAACnC;QACzC;IACF;IAEA,OAAOwB;AACT;AAEA,SAASR,0CACPhB,iBAAyC,EACzCF,KAAc;IAEd,MAAM6B,gBAAgBR,cAAcI,GAAG,CAACvB;IACxC,IAAI2B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAU,IAAII,QAAgC,CAACC,UACnD1C,kBAAkB,IAAM0C,QAAQ7B;IAElCmB,cAAcO,GAAG,CAAC1B,mBAAmBwB;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/B,CAACQ,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMU,aAAa;gBACnBC,UAAU/C,OAAO8C;gBACjB,OAAO5C,iBAAiB,CAACgC,OAAOC,QAAQ,CAAC,GACrCjC,iBAAiB,CAACgC,OAAOC,QAAQ,CAAC,CAACa,KAAK,CACtC9C,mBACA+C,aAGF,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEX,kCAAkCY,IAAI,CAAChD;YAC7C;YACAiD,UAAU;QACZ;QACAZ,MAAM;YACJd;gBACE,MAAMqB,aAAa;gBACnBC,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkBqC,IAAI;YAC/B;QACF;QACAd,KAAK;YACHW,OAAO,SAASX;gBACd,IAAIqB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAF,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkBuB,GAAG,CAACuB,KAAK,CAAC9C,mBAAmB+C;YACxD;YACAE,UAAU;QACZ;QACAX,QAAQ;YACNJ,OAAO,SAASI;gBACd,IAAIM;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAF,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkBsC,MAAM,CAACQ,KAAK,CACnC9C,mBACA+C;YAEJ;YACAE,UAAU;QACZ;QACAV,KAAK;YACHL,OAAO,SAASX;gBACd,IAAIqB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAF,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkBuC,GAAG,CAACO,KAAK,CAAC9C,mBAAmB+C;YACxD;YACAE,UAAU;QACZ;QACAvB,KAAK;YACHQ,OAAO,SAASR;gBACd,IAAIkB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMQ,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPR,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBC,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLR,aAAa;oBACf;gBACF;gBACAC,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkB0B,GAAG,CAACoB,KAAK,CAAC9C,mBAAmB+C;YACxD;YACAE,UAAU;QACZ;QACAT,QAAQ;YACNN,OAAO;gBACL,IAAIU;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLH,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAF,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkBwC,MAAM,CAACM,KAAK,CACnC9C,mBACA+C;YAEJ;YACAE,UAAU;QACZ;QACAR,OAAO;YACLP,OAAO,SAASO;gBACd,MAAMG,aAAa;gBACnBC,UAAU/C,OAAO8C;gBACjB,mFAAmF;gBACnF,OAAO,OAAO5C,kBAAkByC,KAAK,KAAK,aAEtCzC,kBAAkByC,KAAK,CAACK,KAAK,CAAC9C,mBAAmB+C,aAEjD,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEL,+BAA+BM,IAAI,CAAChD,mBAAmBwB;YAC7D;YACAyB,UAAU;QACZ;QACAN,UAAU;YACRT,OAAO,SAASS;gBACd,MAAMC,aAAa;gBACnBC,UAAU/C,OAAO8C;gBACjB,OAAO5C,kBAAkB2C,QAAQ,CAACG,KAAK,CACrC9C,mBACA+C;YAEJ;YACAE,UAAU;QACZ;IACF;IAEA,OAAOzB;AACT;AAEA,+EAA+E;AAC/E,+EAA+E;AAC/E,SAAST,oCACPf,iBAAyC,EACzCF,KAAc;IAEd,MAAM6B,gBAAgBR,cAAcI,GAAG,CAACvB;IACxC,IAAI2B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAU,IAAII,QAAgC,CAACC,UACnD1C,kBAAkB,IAAM0C,QAAQ7B;IAGlC,MAAMqD,iBAAiB,IAAIC,MAAM9B,SAAS;QACxCD,KAAIgC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAKxB,OAAOC,QAAQ;oBAAE;wBACpByB,kBAAkB5D,OAAO;wBACzB;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAY;wBACf4D,kBAAkB5D,OAAO,CAAC,YAAY,EAAE0D,KAAK,EAAE,CAAC;wBAChD;oBACF;gBACA;oBAAS;oBACP,kEAAkE;oBACpE;YACF;YAEA,OAAOlE,eAAeiC,GAAG,CAACgC,QAAQC,MAAMC;QAC1C;IACF;IAEAtC,cAAcO,GAAG,CAAC1B,mBAAmBqD;IAErC,OAAOA;AACT;AAEA,SAASF,gBAAgBC,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAO,AAACA,IAAYO,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAE,AAACP,IAAYO,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOP,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASP,UAAU/C,KAAyB,EAAE8C,UAAkB;IAC9D,MAAMjD,gBAAgBjB,qBAAqBgB,QAAQ;IACnD,IACEC,iBACAA,cAAcQ,IAAI,KAAK,aACvBR,cAAciE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMpD,eAAeb;QACrBb,uCAAuC0B;IACzC;IACA,gCAAgC;IAChCkD,kBAAkB5D,OAAO8C;AAC3B;AAEA,MAAMc,oBAAoBxE,4CACxB2E;AAGF,SAASA,yBACP/D,KAAyB,EACzB8C,UAAkB;IAElB,MAAMkB,SAAShE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGiE,OAAO,KAAK,EAAElB,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASR;IAGP,OAAO,IAAI,CAACE,MAAM,GACfyB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEL,IAAI;YAAEK;SAAE,EACtBC,MAAM;AACX;AAEA,SAASvB,+BAEPwB,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAAC7B,MAAM,GAAI;QAClC,IAAI,CAACE,MAAM,CAAC2B,OAAOR,IAAI;IACzB;IACA,OAAOO;AACT", "ignoreList": [0]}