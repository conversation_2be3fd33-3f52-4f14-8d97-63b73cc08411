{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/index.ts"], "sourcesContent": ["export { originConsoleError } from './intercept-console-error'\nexport { handleClientError } from './use-error-handler'\nexport { decorateDevError } from './stitched-error'\n"], "names": ["decorateDevError", "handleClientError", "originConsoleError"], "mappings": ";;;;;;;;;;;;;;;;IAESA,gBAAgB;eAAhBA,+BAAgB;;IADhBC,iBAAiB;eAAjBA,kCAAiB;;IADjBC,kBAAkB;eAAlBA,yCAAkB;;;uCAAQ;iCACD;+BACD", "ignoreList": [0]}