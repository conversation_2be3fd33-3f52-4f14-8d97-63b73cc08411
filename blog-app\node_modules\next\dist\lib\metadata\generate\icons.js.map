{"version": 3, "sources": ["../../../../src/lib/metadata/generate/icons.tsx"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { Icon, IconDescriptor } from '../types/metadata-types'\nimport { IconMark } from './icon-mark'\n\nimport { MetaFilter } from './meta'\n\nfunction IconDescriptorLink({ icon }: { icon: IconDescriptor }) {\n  const { url, rel = 'icon', ...props } = icon\n\n  return <link rel={rel} href={url.toString()} {...props} />\n}\n\nfunction IconLink({ rel, icon }: { rel?: string; icon: Icon }) {\n  if (typeof icon === 'object' && !(icon instanceof URL)) {\n    if (!icon.rel && rel) icon.rel = rel\n    return IconDescriptorLink({ icon })\n  } else {\n    const href = icon.toString()\n    return <link rel={rel} href={href} />\n  }\n}\n\nexport function IconsMetadata({ icons }: { icons: ResolvedMetadata['icons'] }) {\n  if (!icons) return null\n\n  const shortcutList = icons.shortcut\n  const iconList = icons.icon\n  const appleList = icons.apple\n  const otherList = icons.other\n\n  const hasIcon = Boolean(\n    shortcutList?.length ||\n      iconList?.length ||\n      appleList?.length ||\n      otherList?.length\n  )\n  if (!hasIcon) return null\n\n  return MetaFilter([\n    shortcutList\n      ? shortcutList.map((icon) => IconLink({ rel: 'shortcut icon', icon }))\n      : null,\n    iconList ? iconList.map((icon) => IconLink({ rel: 'icon', icon })) : null,\n    appleList\n      ? appleList.map((icon) => IconLink({ rel: 'apple-touch-icon', icon }))\n      : null,\n    otherList ? otherList.map((icon) => IconDescriptorLink({ icon })) : null,\n    hasIcon ? <IconMark /> : null,\n  ])\n}\n"], "names": ["IconsMetadata", "IconDescriptorLink", "icon", "url", "rel", "props", "link", "href", "toString", "IconLink", "URL", "icons", "shortcutList", "shortcut", "iconList", "appleList", "apple", "otherList", "other", "hasIcon", "Boolean", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "IconMark"], "mappings": ";;;;+BAsBgBA;;;eAAAA;;;;0BApBS;sBAEE;AAE3B,SAASC,mBAAmB,EAAEC,IAAI,EAA4B;IAC5D,MAAM,EAAEC,GAAG,EAAEC,MAAM,MAAM,EAAE,GAAGC,OAAO,GAAGH;IAExC,qBAAO,qBAACI;QAAKF,KAAKA;QAAKG,MAAMJ,IAAIK,QAAQ;QAAK,GAAGH,KAAK;;AACxD;AAEA,SAASI,SAAS,EAAEL,GAAG,EAAEF,IAAI,EAAgC;IAC3D,IAAI,OAAOA,SAAS,YAAY,CAAEA,CAAAA,gBAAgBQ,GAAE,GAAI;QACtD,IAAI,CAACR,KAAKE,GAAG,IAAIA,KAAKF,KAAKE,GAAG,GAAGA;QACjC,OAAOH,mBAAmB;YAAEC;QAAK;IACnC,OAAO;QACL,MAAMK,OAAOL,KAAKM,QAAQ;QAC1B,qBAAO,qBAACF;YAAKF,KAAKA;YAAKG,MAAMA;;IAC/B;AACF;AAEO,SAASP,cAAc,EAAEW,KAAK,EAAwC;IAC3E,IAAI,CAACA,OAAO,OAAO;IAEnB,MAAMC,eAAeD,MAAME,QAAQ;IACnC,MAAMC,WAAWH,MAAMT,IAAI;IAC3B,MAAMa,YAAYJ,MAAMK,KAAK;IAC7B,MAAMC,YAAYN,MAAMO,KAAK;IAE7B,MAAMC,UAAUC,QACdR,CAAAA,gCAAAA,aAAcS,MAAM,MAClBP,4BAAAA,SAAUO,MAAM,MAChBN,6BAAAA,UAAWM,MAAM,MACjBJ,6BAAAA,UAAWI,MAAM;IAErB,IAAI,CAACF,SAAS,OAAO;IAErB,OAAOG,IAAAA,gBAAU,EAAC;QAChBV,eACIA,aAAaW,GAAG,CAAC,CAACrB,OAASO,SAAS;gBAAEL,KAAK;gBAAiBF;YAAK,MACjE;QACJY,WAAWA,SAASS,GAAG,CAAC,CAACrB,OAASO,SAAS;gBAAEL,KAAK;gBAAQF;YAAK,MAAM;QACrEa,YACIA,UAAUQ,GAAG,CAAC,CAACrB,OAASO,SAAS;gBAAEL,KAAK;gBAAoBF;YAAK,MACjE;QACJe,YAAYA,UAAUM,GAAG,CAAC,CAACrB,OAASD,mBAAmB;gBAAEC;YAAK,MAAM;QACpEiB,wBAAU,qBAACK,kBAAQ,QAAM;KAC1B;AACH", "ignoreList": [0]}