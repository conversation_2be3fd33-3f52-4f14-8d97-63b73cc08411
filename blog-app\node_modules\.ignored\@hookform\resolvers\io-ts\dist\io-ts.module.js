import{toNestErrors as n,validateFieldsNatively as t}from"@hookform/resolvers";import*as r from"fp-ts/Either";import{pipe as e,flow as o,absurd as u,identity as a,not as i}from"fp-ts/function";import*as f from"fp-ts/Option";import*as s from"fp-ts/ReadonlyArray";import*as c from"fp-ts/ReadonlyRecord";import*as p from"fp-ts/Semigroup";import{TaggedUnionType as m,UnionType as l,IntersectionType as d,ExactType as v,RefinementType as y}from"io-ts";function g(){return g=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var e in r)({}).hasOwnProperty.call(r,e)&&(n[e]=r[e])}return n},g.apply(null,arguments)}var h=function(n){return n.reduce(function(n,t,o){return e(t,r.fold(function(n){return(o>0?".":"")+n},function(n){return"["+n+"]"}),function(t){return""+n+t})},"")},O=["path"],b=[m,l,d,v,y],x=function(n){var t,c=e(t=n.context,s.filterMapWithIndex(function(n,r){var e=n-1,o=-1===e?void 0:t[e];return void 0===o||b.some(function(n){return o.type instanceof n})?f.none:f.some(r)}),s.map(function(n){return n.key}),s.map(function(n){return e(n,function(n){return parseInt(n,10)},r.fromPredicate(i(Number.isNaN),function(){return n}))}),s.toArray,h);return{message:e(n.message,r.fromNullable(n.context),r.mapLeft(o(s.last,f.map(function(n){return"expected "+n.type.name+" but got "+JSON.stringify(n.actual)}),f.getOrElseW(function(){return u("Error context is missing name")}))),r.getOrElseW(a)),type:e(n.context,s.last,f.map(function(n){return n.type.name}),f.getOrElse(function(){return"unknown"})),path:c}},N=function(n){return e(n,s.map(function(n){var t;return(t={})[n.path]={type:n.type,message:n.message},t}),function(n){return p.fold({concat:function(n,t){return Object.assign({},t,n)}})({},n)})},E={concat:function(n,t){var r;return g({},t,{types:g({},n.types,(r={},r[n.type]=n.message,r[t.type]=t.message,r))})}},j=function(n){return e(c.fromFoldableMap(E,s.Foldable)(n,function(n){return[n.path,n]}),c.map(function(n){return function(n,t){if(null==n)return{};var r={};for(var e in n)if({}.hasOwnProperty.call(n,e)){if(-1!==t.indexOf(e))continue;r[e]=n[e]}return r}(n,O)}))};function k(o){return function(u,a,i){return e(u,o.decode,r.mapLeft((f=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,function(n){var t=f?j:N;return e(n,s.map(x),t)})),r.mapLeft(function(t){return n(t,i)}),r.fold(function(n){return{values:{},errors:n}},function(n){return i.shouldUseNativeValidation&&t({},i),{values:n,errors:{}}}));var f}}export{k as ioTsResolver};
//# sourceMappingURL=io-ts.module.js.map
