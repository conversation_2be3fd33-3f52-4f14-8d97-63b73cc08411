{"version": 3, "sources": ["../../../src/server/app-render/app-render-prerender-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setImmediate) at the same time to ensure no other events can sneak in between.\n */\nexport function prerenderAndAbortInSequentialTasks<R>(\n  prerender: () => Promise<R>,\n  abort: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = prerender()\n          pendingResult.catch(() => {})\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        abort()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n\nconst PENDING = 0\nconst COMPLETE = 1\nconst INTERRUPTED = 2\nconst ERRORED = 3\n\nexport class ServerPrerenderStreamResult {\n  private currentChunks: Array<Uint8Array>\n  private chunksByPhase: Array<Array<Uint8Array>>\n  private trailingChunks: Array<Uint8Array>\n  private status: 0 | 1 | 2 | 3\n  private reason: null | unknown\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    this.status = PENDING\n    this.reason = null\n\n    this.trailingChunks = []\n    this.currentChunks = []\n    this.chunksByPhase = [this.currentChunks]\n\n    const reader = stream.getReader()\n\n    const progress = ({\n      done,\n      value,\n    }: ReadableStreamReadResult<Uint8Array>) => {\n      if (done) {\n        if (this.status === PENDING) {\n          this.status = COMPLETE\n        }\n        return\n      }\n      if (this.status === PENDING || this.status === INTERRUPTED) {\n        this.currentChunks.push(value)\n      } else {\n        this.trailingChunks.push(value)\n      }\n      reader.read().then(progress, error)\n    }\n    const error = (reason: unknown) => {\n      this.status = ERRORED\n      this.reason = reason\n    }\n\n    reader.read().then(progress, error)\n  }\n\n  markPhase() {\n    this.currentChunks = []\n    this.chunksByPhase.push(this.currentChunks)\n  }\n\n  markComplete() {\n    if (this.status === PENDING) {\n      this.status = COMPLETE\n    }\n  }\n\n  markInterrupted() {\n    this.status = INTERRUPTED\n  }\n\n  /**\n   * Returns a stream which only releases chunks when `releasePhase` is called. This stream will never \"complete\" because\n   * we rely upon the stream remaining open when prerendering to avoid triggering errors for incomplete chunks in the client.\n   *\n   * asPhasedStream is expected to be called once per result however it is safe to call multiple times as long as we have not\n   * transferred the underlying data. Generally this will only happen when streaming to a response\n   */\n  asPhasedStream() {\n    switch (this.status) {\n      case COMPLETE:\n      case INTERRUPTED:\n        return new PhasedStream(this.chunksByPhase)\n      default:\n        throw new InvariantError(\n          `ServerPrerenderStreamResult cannot be consumed as a stream because it is not yet complete. status: ${this.status}`\n        )\n    }\n  }\n\n  /**\n   * Returns a stream which will release all chunks immediately. This stream will \"complete\" synchronously. It should be used outside\n   * of render use cases like loading client chunks ahead of SSR or writing the streamed content to disk.\n   */\n  asStream() {\n    switch (this.status) {\n      case COMPLETE:\n      case INTERRUPTED:\n        const chunksByPhase = this.chunksByPhase\n        const trailingChunks = this.trailingChunks\n        return new ReadableStream({\n          start(controller) {\n            for (let i = 0; i < chunksByPhase.length; i++) {\n              const chunks = chunksByPhase[i]\n              for (let j = 0; j < chunks.length; j++) {\n                controller.enqueue(chunks[j])\n              }\n            }\n            for (let i = 0; i < trailingChunks.length; i++) {\n              controller.enqueue(trailingChunks[i])\n            }\n            controller.close()\n          },\n        })\n      default:\n        throw new InvariantError(\n          `ServerPrerenderStreamResult cannot be consumed as a stream because it is not yet complete. status: ${this.status}`\n        )\n    }\n  }\n}\n\nclass PhasedStream<T> extends ReadableStream<T> {\n  private nextPhase: number\n  private chunksByPhase: Array<Array<T>>\n  private destination: ReadableStreamDefaultController<T>\n\n  constructor(chunksByPhase: Array<Array<T>>) {\n    if (chunksByPhase.length === 0) {\n      throw new InvariantError(\n        'PhasedStream expected at least one phase but none were found.'\n      )\n    }\n\n    let destination: ReadableStreamDefaultController<T>\n    super({\n      start(controller) {\n        destination = controller\n      },\n    })\n\n    // the start function above is called synchronously during construction so we will always have a destination\n    // We wait to assign it until after the super call because we cannot access `this` before calling super\n    this.destination = destination!\n    this.nextPhase = 0\n    this.chunksByPhase = chunksByPhase\n    this.releasePhase()\n  }\n\n  releasePhase() {\n    if (this.nextPhase < this.chunksByPhase.length) {\n      const chunks = this.chunksByPhase[this.nextPhase++]\n      for (let i = 0; i < chunks.length; i++) {\n        this.destination.enqueue(chunks[i])\n      }\n    } else {\n      throw new InvariantError(\n        'PhasedStream expected more phases to release but none were found.'\n      )\n    }\n  }\n\n  assertExhausted() {\n    if (this.nextPhase < this.chunksByPhase.length) {\n      throw new InvariantError(\n        'PhasedStream expected no more phases to release but some were found.'\n      )\n    }\n  }\n}\n\n// React's RSC prerender function will emit an incomplete flight stream when using `prerender`. If the connection\n// closes then whatever hanging chunks exist will be errored. This is because prerender (an experimental feature)\n// has not yet implemented a concept of resume. For now we will simulate a paused connection by wrapping the stream\n// in one that doesn't close even when the underlying is complete.\nexport class ReactServerResult {\n  private _stream: null | ReadableStream<Uint8Array>\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    this._stream = stream\n  }\n\n  tee() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot tee a ReactServerResult that has already been consumed'\n      )\n    }\n    const tee = this._stream.tee()\n    this._stream = tee[0]\n    return tee[1]\n  }\n\n  consume() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot consume a ReactServerResult that has already been consumed'\n      )\n    }\n    const stream = this._stream\n    this._stream = null\n    return stream\n  }\n}\n\nexport type ReactServerPrerenderResolveToType = {\n  prelude: ReadableStream<Uint8Array>\n}\n\nexport async function createReactServerPrerenderResult(\n  underlying: Promise<ReactServerPrerenderResolveToType>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const { prelude } = await underlying\n  const reader = prelude.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      return new ReactServerPrerenderResult(chunks)\n    } else {\n      chunks.push(value)\n    }\n  }\n}\n\nexport async function createReactServerPrerenderResultFromRender(\n  underlying: ReadableStream<Uint8Array>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const reader = underlying.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    } else {\n      chunks.push(value)\n    }\n  }\n  return new ReactServerPrerenderResult(chunks)\n}\nexport class ReactServerPrerenderResult {\n  private _chunks: null | Array<Uint8Array>\n\n  private assertChunks(expression: string): Array<Uint8Array> {\n    if (this._chunks === null) {\n      throw new InvariantError(\n        `Cannot \\`${expression}\\` on a ReactServerPrerenderResult that has already been consumed.`\n      )\n    }\n    return this._chunks\n  }\n\n  private consumeChunks(expression: string): Array<Uint8Array> {\n    const chunks = this.assertChunks(expression)\n    this.consume()\n    return chunks\n  }\n\n  consume(): void {\n    this._chunks = null\n  }\n\n  constructor(chunks: Array<Uint8Array>) {\n    this._chunks = chunks\n  }\n\n  asUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  consumeAsUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  asStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asStream()')\n    return createClosingStream(chunks)\n  }\n\n  consumeAsStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsStream()')\n    return createClosingStream(chunks)\n  }\n}\n\nfunction createUnclosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      }\n      // we intentionally keep the stream open. The consumer will clear\n      // out chunks once finished and the remaining memory will be GC'd\n      // when this object goes out of scope\n    },\n  })\n}\n\nfunction createClosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      } else {\n        controller.close()\n      }\n    },\n  })\n}\n\nexport async function processPrelude(\n  unprocessedPrelude: ReadableStream<Uint8Array>\n) {\n  const [prelude, peek] = unprocessedPrelude.tee()\n\n  const reader = peek.getReader()\n  const firstResult = await reader.read()\n  reader.cancel()\n\n  const preludeIsEmpty = firstResult.done === true\n\n  return { prelude, preludeIsEmpty }\n}\n"], "names": ["ReactServerPrerenderResult", "ReactServerResult", "ServerPrerenderStreamResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "processPrelude", "prerender", "abort", "process", "env", "NEXT_RUNTIME", "InvariantError", "Promise", "resolve", "reject", "pendingResult", "setImmediate", "catch", "err", "PENDING", "COMPLETE", "INTERRUPTED", "ERRORED", "constructor", "stream", "status", "reason", "trailingChunks", "currentChunks", "chunksByPhase", "reader", "<PERSON><PERSON><PERSON><PERSON>", "progress", "done", "value", "push", "read", "then", "error", "<PERSON><PERSON><PERSON>e", "markComplete", "markInterrupted", "asPhasedStream", "PhasedStream", "asStream", "ReadableStream", "start", "controller", "i", "length", "chunks", "j", "enqueue", "close", "destination", "nextPhase", "releasePhase", "assertExhausted", "_stream", "tee", "Error", "consume", "underlying", "prelude", "assertChunks", "expression", "_chunks", "consumeChunks", "asUnclosingStream", "createUnclosingStream", "consumeAsUnclosingStream", "createClosingStream", "consumeAsStream", "pull", "unprocessedPrelude", "peek", "firstResult", "cancel", "preludeIsEmpty"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAwQaA,0BAA0B;eAA1BA;;IAjEAC,iBAAiB;eAAjBA;;IAjKAC,2BAA2B;eAA3BA;;IAmMSC,gCAAgC;eAAhCA;;IAgBAC,0CAA0C;eAA1CA;;IAnPNC,kCAAkC;eAAlCA;;IAgVMC,cAAc;eAAdA;;;gCAtVS;AAMxB,SAASD,mCACdE,SAA2B,EAC3BC,KAAiB;IAEjB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,+EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBT;oBAChBS,cAAcE,KAAK,CAAC,KAAO;gBAC7B,EAAE,OAAOC,KAAK;oBACZJ,OAAOI;gBACT;YACF;YACAF,aAAa;gBACXT;gBACAM,QAAQE;YACV;QACF;IACF;AACF;AAEA,MAAMI,UAAU;AAChB,MAAMC,WAAW;AACjB,MAAMC,cAAc;AACpB,MAAMC,UAAU;AAET,MAAMrB;IAOXsB,YAAYC,MAAkC,CAAE;QAC9C,IAAI,CAACC,MAAM,GAAGN;QACd,IAAI,CAACO,MAAM,GAAG;QAEd,IAAI,CAACC,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,aAAa,GAAG,EAAE;QACvB,IAAI,CAACC,aAAa,GAAG;YAAC,IAAI,CAACD,aAAa;SAAC;QAEzC,MAAME,SAASN,OAAOO,SAAS;QAE/B,MAAMC,WAAW,CAAC,EAChBC,IAAI,EACJC,KAAK,EACgC;YACrC,IAAID,MAAM;gBACR,IAAI,IAAI,CAACR,MAAM,KAAKN,SAAS;oBAC3B,IAAI,CAACM,MAAM,GAAGL;gBAChB;gBACA;YACF;YACA,IAAI,IAAI,CAACK,MAAM,KAAKN,WAAW,IAAI,CAACM,MAAM,KAAKJ,aAAa;gBAC1D,IAAI,CAACO,aAAa,CAACO,IAAI,CAACD;YAC1B,OAAO;gBACL,IAAI,CAACP,cAAc,CAACQ,IAAI,CAACD;YAC3B;YACAJ,OAAOM,IAAI,GAAGC,IAAI,CAACL,UAAUM;QAC/B;QACA,MAAMA,QAAQ,CAACZ;YACb,IAAI,CAACD,MAAM,GAAGH;YACd,IAAI,CAACI,MAAM,GAAGA;QAChB;QAEAI,OAAOM,IAAI,GAAGC,IAAI,CAACL,UAAUM;IAC/B;IAEAC,YAAY;QACV,IAAI,CAACX,aAAa,GAAG,EAAE;QACvB,IAAI,CAACC,aAAa,CAACM,IAAI,CAAC,IAAI,CAACP,aAAa;IAC5C;IAEAY,eAAe;QACb,IAAI,IAAI,CAACf,MAAM,KAAKN,SAAS;YAC3B,IAAI,CAACM,MAAM,GAAGL;QAChB;IACF;IAEAqB,kBAAkB;QAChB,IAAI,CAAChB,MAAM,GAAGJ;IAChB;IAEA;;;;;;GAMC,GACDqB,iBAAiB;QACf,OAAQ,IAAI,CAACjB,MAAM;YACjB,KAAKL;YACL,KAAKC;gBACH,OAAO,IAAIsB,aAAa,IAAI,CAACd,aAAa;YAC5C;gBACE,MAAM,qBAEL,CAFK,IAAIlB,8BAAc,CACtB,CAAC,mGAAmG,EAAE,IAAI,CAACc,MAAM,EAAE,GAD/G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;QACJ;IACF;IAEA;;;GAGC,GACDmB,WAAW;QACT,OAAQ,IAAI,CAACnB,MAAM;YACjB,KAAKL;YACL,KAAKC;gBACH,MAAMQ,gBAAgB,IAAI,CAACA,aAAa;gBACxC,MAAMF,iBAAiB,IAAI,CAACA,cAAc;gBAC1C,OAAO,IAAIkB,eAAe;oBACxBC,OAAMC,UAAU;wBACd,IAAK,IAAIC,IAAI,GAAGA,IAAInB,cAAcoB,MAAM,EAAED,IAAK;4BAC7C,MAAME,SAASrB,aAAa,CAACmB,EAAE;4BAC/B,IAAK,IAAIG,IAAI,GAAGA,IAAID,OAAOD,MAAM,EAAEE,IAAK;gCACtCJ,WAAWK,OAAO,CAACF,MAAM,CAACC,EAAE;4BAC9B;wBACF;wBACA,IAAK,IAAIH,IAAI,GAAGA,IAAIrB,eAAesB,MAAM,EAAED,IAAK;4BAC9CD,WAAWK,OAAO,CAACzB,cAAc,CAACqB,EAAE;wBACtC;wBACAD,WAAWM,KAAK;oBAClB;gBACF;YACF;gBACE,MAAM,qBAEL,CAFK,IAAI1C,8BAAc,CACtB,CAAC,mGAAmG,EAAE,IAAI,CAACc,MAAM,EAAE,GAD/G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;QACJ;IACF;AACF;AAEA,MAAMkB,qBAAwBE;IAK5BtB,YAAYM,aAA8B,CAAE;QAC1C,IAAIA,cAAcoB,MAAM,KAAK,GAAG;YAC9B,MAAM,qBAEL,CAFK,IAAItC,8BAAc,CACtB,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI2C;QACJ,KAAK,CAAC;YACJR,OAAMC,UAAU;gBACdO,cAAcP;YAChB;QACF;QAEA,4GAA4G;QAC5G,uGAAuG;QACvG,IAAI,CAACO,WAAW,GAAGA;QACnB,IAAI,CAACC,SAAS,GAAG;QACjB,IAAI,CAAC1B,aAAa,GAAGA;QACrB,IAAI,CAAC2B,YAAY;IACnB;IAEAA,eAAe;QACb,IAAI,IAAI,CAACD,SAAS,GAAG,IAAI,CAAC1B,aAAa,CAACoB,MAAM,EAAE;YAC9C,MAAMC,SAAS,IAAI,CAACrB,aAAa,CAAC,IAAI,CAAC0B,SAAS,GAAG;YACnD,IAAK,IAAIP,IAAI,GAAGA,IAAIE,OAAOD,MAAM,EAAED,IAAK;gBACtC,IAAI,CAACM,WAAW,CAACF,OAAO,CAACF,MAAM,CAACF,EAAE;YACpC;QACF,OAAO;YACL,MAAM,qBAEL,CAFK,IAAIrC,8BAAc,CACtB,sEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA8C,kBAAkB;QAChB,IAAI,IAAI,CAACF,SAAS,GAAG,IAAI,CAAC1B,aAAa,CAACoB,MAAM,EAAE;YAC9C,MAAM,qBAEL,CAFK,IAAItC,8BAAc,CACtB,yEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF;AAMO,MAAMX;IAGXuB,YAAYC,MAAkC,CAAE;QAC9C,IAAI,CAACkC,OAAO,GAAGlC;IACjB;IAEAmC,MAAM;QACJ,IAAI,IAAI,CAACD,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMD,MAAM,IAAI,CAACD,OAAO,CAACC,GAAG;QAC5B,IAAI,CAACD,OAAO,GAAGC,GAAG,CAAC,EAAE;QACrB,OAAOA,GAAG,CAAC,EAAE;IACf;IAEAE,UAAU;QACR,IAAI,IAAI,CAACH,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,sEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMpC,SAAS,IAAI,CAACkC,OAAO;QAC3B,IAAI,CAACA,OAAO,GAAG;QACf,OAAOlC;IACT;AACF;AAMO,eAAetB,iCACpB4D,UAAsD;IAEtD,MAAMZ,SAA4B,EAAE;IACpC,MAAM,EAAEa,OAAO,EAAE,GAAG,MAAMD;IAC1B,MAAMhC,SAASiC,QAAQhC,SAAS;IAChC,MAAO,KAAM;QACX,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOM,IAAI;QACzC,IAAIH,MAAM;YACR,OAAO,IAAIlC,2BAA2BmD;QACxC,OAAO;YACLA,OAAOf,IAAI,CAACD;QACd;IACF;AACF;AAEO,eAAe/B,2CACpB2D,UAAsC;IAEtC,MAAMZ,SAA4B,EAAE;IACpC,MAAMpB,SAASgC,WAAW/B,SAAS;IACnC,MAAO,KAAM;QACX,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOM,IAAI;QACzC,IAAIH,MAAM;YACR;QACF,OAAO;YACLiB,OAAOf,IAAI,CAACD;QACd;IACF;IACA,OAAO,IAAInC,2BAA2BmD;AACxC;AACO,MAAMnD;IAGHiE,aAAaC,UAAkB,EAAqB;QAC1D,IAAI,IAAI,CAACC,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIvD,8BAAc,CACtB,CAAC,SAAS,EAAEsD,WAAW,kEAAkE,CAAC,GADtF,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQC,cAAcF,UAAkB,EAAqB;QAC3D,MAAMf,SAAS,IAAI,CAACc,YAAY,CAACC;QACjC,IAAI,CAACJ,OAAO;QACZ,OAAOX;IACT;IAEAW,UAAgB;QACd,IAAI,CAACK,OAAO,GAAG;IACjB;IAEA3C,YAAY2B,MAAyB,CAAE;QACrC,IAAI,CAACgB,OAAO,GAAGhB;IACjB;IAEAkB,oBAAgD;QAC9C,MAAMlB,SAAS,IAAI,CAACc,YAAY,CAAC;QACjC,OAAOK,sBAAsBnB;IAC/B;IAEAoB,2BAAuD;QACrD,MAAMpB,SAAS,IAAI,CAACiB,aAAa,CAAC;QAClC,OAAOE,sBAAsBnB;IAC/B;IAEAN,WAAuC;QACrC,MAAMM,SAAS,IAAI,CAACc,YAAY,CAAC;QACjC,OAAOO,oBAAoBrB;IAC7B;IAEAsB,kBAA8C;QAC5C,MAAMtB,SAAS,IAAI,CAACiB,aAAa,CAAC;QAClC,OAAOI,oBAAoBrB;IAC7B;AACF;AAEA,SAASmB,sBACPnB,MAAyB;IAEzB,IAAIF,IAAI;IACR,OAAO,IAAIH,eAAe;QACxB,MAAM4B,MAAK1B,UAAU;YACnB,IAAIC,IAAIE,OAAOD,MAAM,EAAE;gBACrBF,WAAWK,OAAO,CAACF,MAAM,CAACF,IAAI;YAChC;QACA,iEAAiE;QACjE,iEAAiE;QACjE,qCAAqC;QACvC;IACF;AACF;AAEA,SAASuB,oBACPrB,MAAyB;IAEzB,IAAIF,IAAI;IACR,OAAO,IAAIH,eAAe;QACxB,MAAM4B,MAAK1B,UAAU;YACnB,IAAIC,IAAIE,OAAOD,MAAM,EAAE;gBACrBF,WAAWK,OAAO,CAACF,MAAM,CAACF,IAAI;YAChC,OAAO;gBACLD,WAAWM,KAAK;YAClB;QACF;IACF;AACF;AAEO,eAAehD,eACpBqE,kBAA8C;IAE9C,MAAM,CAACX,SAASY,KAAK,GAAGD,mBAAmBf,GAAG;IAE9C,MAAM7B,SAAS6C,KAAK5C,SAAS;IAC7B,MAAM6C,cAAc,MAAM9C,OAAOM,IAAI;IACrCN,OAAO+C,MAAM;IAEb,MAAMC,iBAAiBF,YAAY3C,IAAI,KAAK;IAE5C,OAAO;QAAE8B;QAASe;IAAe;AACnC", "ignoreList": [0]}