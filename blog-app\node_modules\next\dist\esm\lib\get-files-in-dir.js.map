{"version": 3, "sources": ["../../src/lib/get-files-in-dir.ts"], "sourcesContent": ["import { join } from 'path'\nimport fs from 'fs/promises'\nimport type { Dirent, StatsBase } from 'fs'\n\nexport async function getFilesInDir(path: string): Promise<Set<string>> {\n  const dir = await fs.opendir(path)\n  const results = new Set<string>()\n\n  for await (const file of dir) {\n    let resolvedFile: Dirent | StatsBase<number> = file\n\n    if (file.isSymbolicLink()) {\n      resolvedFile = await fs.stat(join(path, file.name))\n    }\n\n    if (resolvedFile.isFile()) {\n      results.add(file.name)\n    }\n  }\n\n  return results\n}\n"], "names": ["join", "fs", "getFilesInDir", "path", "dir", "opendir", "results", "Set", "file", "resolvedFile", "isSymbolicLink", "stat", "name", "isFile", "add"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAM;AAC3B,OAAOC,QAAQ,cAAa;AAG5B,OAAO,eAAeC,cAAcC,IAAY;IAC9C,MAAMC,MAAM,MAAMH,GAAGI,OAAO,CAACF;IAC7B,MAAMG,UAAU,IAAIC;IAEpB,WAAW,MAAMC,QAAQJ,IAAK;QAC5B,IAAIK,eAA2CD;QAE/C,IAAIA,KAAKE,cAAc,IAAI;YACzBD,eAAe,MAAMR,GAAGU,IAAI,CAACX,KAAKG,MAAMK,KAAKI,IAAI;QACnD;QAEA,IAAIH,aAAaI,MAAM,IAAI;YACzBP,QAAQQ,GAAG,CAACN,KAAKI,IAAI;QACvB;IACF;IAEA,OAAON;AACT", "ignoreList": [0]}