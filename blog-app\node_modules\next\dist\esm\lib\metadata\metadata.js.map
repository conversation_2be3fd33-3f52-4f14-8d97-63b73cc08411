{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "sourcesContent": ["import React, { Suspense, cache, cloneElement } from 'react'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { StreamingMetadataResolvedState } from '../../client/components/metadata/types'\nimport type { SearchParams } from '../../server/request/search-params'\nimport {\n  AppleWebAppMeta,\n  FormatDetectionMeta,\n  ItunesMeta,\n  BasicMeta,\n  ViewportMeta,\n  VerificationMeta,\n  FacebookMeta,\n  PinterestMeta,\n} from './generate/basic'\nimport { AlternatesMetadata } from './generate/alternate'\nimport {\n  OpenGraphMetadata,\n  TwitterMetadata,\n  AppLinksMeta,\n} from './generate/opengraph'\nimport { IconsMetadata } from './generate/icons'\nimport {\n  type MetadataErrorType,\n  resolveMetadata,\n  resolveViewport,\n} from './resolve-metadata'\nimport { MetaFilter } from './generate/meta'\nimport type {\n  ResolvedMetadata,\n  ResolvedViewport,\n} from './types/metadata-interface'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport type { MetadataContext } from './types/resolvers'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n} from './metadata-constants'\nimport { AsyncMetadataOutlet } from '../../client/components/metadata/async-metadata'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { createServerSearchParamsForMetadata } from '../../server/request/search-params'\nimport { createServerPathnameForMetadata } from '../../server/request/pathname'\n\n// Use a promise to share the status of the metadata resolving,\n// returning two components `MetadataTree` and `MetadataOutlet`\n// `MetadataTree` is the one that will be rendered at first in the content sequence for metadata tags.\n// `MetadataOutlet` is the one that will be rendered under error boundaries for metadata resolving errors.\n// In this way we can let the metadata tags always render successfully,\n// and the error will be caught by the error boundary and trigger fallbacks.\nexport function createMetadataComponents({\n  tree,\n  pathname,\n  parsedQuery,\n  metadataContext,\n  getDynamicParamFromSegment,\n  appUsingSizeAdjustment,\n  errorType,\n  workStore,\n  MetadataBoundary,\n  ViewportBoundary,\n  serveStreamingMetadata,\n}: {\n  tree: LoaderTree\n  pathname: string\n  parsedQuery: SearchParams\n  metadataContext: MetadataContext\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  appUsingSizeAdjustment: boolean\n  errorType?: MetadataErrorType | 'redirect'\n  workStore: WorkStore\n  MetadataBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  ViewportBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  serveStreamingMetadata: boolean\n}): {\n  MetadataTree: React.ComponentType\n  ViewportTree: React.ComponentType\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  StreamingMetadataOutlet: React.ComponentType | null\n} {\n  const searchParams = createServerSearchParamsForMetadata(\n    parsedQuery,\n    workStore\n  )\n  const pathnameForMetadata = createServerPathnameForMetadata(\n    pathname,\n    workStore\n  )\n\n  function ViewportTree() {\n    return (\n      <>\n        <ViewportBoundary>\n          <Viewport />\n        </ViewportBoundary>\n        {/* This meta tag is for next/font which is still required to be blocking. */}\n        {appUsingSizeAdjustment ? (\n          <meta name=\"next-size-adjust\" content=\"\" />\n        ) : null}\n      </>\n    )\n  }\n\n  function MetadataTree() {\n    return (\n      <MetadataBoundary>\n        <Metadata />\n      </MetadataBoundary>\n    )\n  }\n\n  function viewport() {\n    return getResolvedViewport(\n      tree,\n      searchParams,\n      getDynamicParamFromSegment,\n      workStore,\n      errorType\n    )\n  }\n\n  async function Viewport() {\n    try {\n      return await viewport()\n    } catch (error) {\n      if (!errorType && isHTTPAccessFallbackError(error)) {\n        try {\n          return await getNotFoundViewport(\n            tree,\n            searchParams,\n            getDynamicParamFromSegment,\n            workStore\n          )\n        } catch {}\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return null\n    }\n  }\n  Viewport.displayName = VIEWPORT_BOUNDARY_NAME\n\n  function metadata() {\n    return getResolvedMetadata(\n      tree,\n      pathnameForMetadata,\n      searchParams,\n      getDynamicParamFromSegment,\n      metadataContext,\n      workStore,\n      errorType\n    )\n  }\n\n  async function resolveFinalMetadata(): Promise<StreamingMetadataResolvedState> {\n    let result: React.ReactNode\n    let error = null\n    try {\n      result = await metadata()\n      return {\n        metadata: result,\n        error: null,\n        digest: undefined,\n      }\n    } catch (metadataErr) {\n      error = metadataErr\n      if (!errorType && isHTTPAccessFallbackError(metadataErr)) {\n        try {\n          result = await getNotFoundMetadata(\n            tree,\n            pathnameForMetadata,\n            searchParams,\n            getDynamicParamFromSegment,\n            metadataContext,\n            workStore\n          )\n          return {\n            metadata: result,\n            error,\n            digest: (error as any)?.digest,\n          }\n        } catch (notFoundMetadataErr) {\n          error = notFoundMetadataErr\n          // In PPR rendering we still need to throw the postpone error.\n          // If metadata is postponed, React needs to be aware of the location of error.\n          if (serveStreamingMetadata && isPostpone(notFoundMetadataErr)) {\n            throw notFoundMetadataErr\n          }\n        }\n      }\n      // In PPR rendering we still need to throw the postpone error.\n      // If metadata is postponed, React needs to be aware of the location of error.\n      if (serveStreamingMetadata && isPostpone(metadataErr)) {\n        throw metadataErr\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return {\n        metadata: result,\n        error,\n        digest: (error as any)?.digest,\n      }\n    }\n  }\n\n  function Metadata() {\n    if (!serveStreamingMetadata) {\n      return <MetadataResolver />\n    }\n    return (\n      <div hidden>\n        <Suspense fallback={null}>\n          <MetadataResolver />\n        </Suspense>\n      </div>\n    )\n  }\n\n  async function MetadataResolver() {\n    const metadataState = await resolveFinalMetadata()\n    return metadataState.metadata\n  }\n\n  Metadata.displayName = METADATA_BOUNDARY_NAME\n\n  async function getMetadataReady(): Promise<void> {\n    // Only warm up metadata() call when it's blocking metadata,\n    // otherwise it will be fully managed by AsyncMetadata component.\n    if (!serveStreamingMetadata) {\n      await metadata()\n    }\n    return undefined\n  }\n\n  async function getViewportReady(): Promise<void> {\n    await viewport()\n    return undefined\n  }\n\n  function StreamingMetadataOutletImpl() {\n    return <AsyncMetadataOutlet promise={resolveFinalMetadata()} />\n  }\n\n  const StreamingMetadataOutlet = serveStreamingMetadata\n    ? StreamingMetadataOutletImpl\n    : null\n\n  return {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  }\n}\n\nconst getResolvedMetadata = cache(getResolvedMetadataImpl)\nasync function getResolvedMetadataImpl(\n  tree: LoaderTree,\n  pathname: Promise<string>,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderMetadata(\n    tree,\n    pathname,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundMetadata = cache(getNotFoundMetadataImpl)\nasync function getNotFoundMetadataImpl(\n  tree: LoaderTree,\n  pathname: Promise<string>,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderMetadata(\n    tree,\n    pathname,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nconst getResolvedViewport = cache(getResolvedViewportImpl)\nasync function getResolvedViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundViewport = cache(getNotFoundViewportImpl)\nasync function getNotFoundViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nasync function renderMetadata(\n  tree: LoaderTree,\n  pathname: Promise<string>,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const resolvedMetadata = await resolveMetadata(\n    tree,\n    pathname,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore,\n    metadataContext\n  )\n  const elements: Array<React.ReactNode> =\n    createMetadataElements(resolvedMetadata)\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nasync function renderViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const resolvedViewport = await resolveViewport(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n\n  const elements: Array<React.ReactNode> =\n    createViewportElements(resolvedViewport)\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nfunction createMetadataElements(metadata: ResolvedMetadata) {\n  return MetaFilter([\n    BasicMeta({ metadata }),\n    AlternatesMetadata({ alternates: metadata.alternates }),\n    ItunesMeta({ itunes: metadata.itunes }),\n    FacebookMeta({ facebook: metadata.facebook }),\n    PinterestMeta({ pinterest: metadata.pinterest }),\n    FormatDetectionMeta({ formatDetection: metadata.formatDetection }),\n    VerificationMeta({ verification: metadata.verification }),\n    AppleWebAppMeta({ appleWebApp: metadata.appleWebApp }),\n    OpenGraphMetadata({ openGraph: metadata.openGraph }),\n    TwitterMetadata({ twitter: metadata.twitter }),\n    AppLinksMeta({ appLinks: metadata.appLinks }),\n    IconsMetadata({ icons: metadata.icons }),\n  ])\n}\n\nfunction createViewportElements(viewport: ResolvedViewport) {\n  return MetaFilter([ViewportMeta({ viewport: viewport })])\n}\n"], "names": ["React", "Suspense", "cache", "cloneElement", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMeta", "ViewportMeta", "VerificationMeta", "FacebookMeta", "PinterestMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "resolveViewport", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isHTTPAccessFallbackError", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "AsyncMetadataOutlet", "isPostpone", "createServerSearchParamsForMetadata", "createServerPathnameForMetadata", "createMetadataComponents", "tree", "pathname", "parsed<PERSON><PERSON><PERSON>", "metadataContext", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "workStore", "MetadataBoundary", "ViewportBoundary", "serveStreamingMetadata", "searchParams", "pathnameForMetadata", "ViewportTree", "Viewport", "meta", "name", "content", "MetadataTree", "<PERSON><PERSON><PERSON>", "viewport", "getResolvedViewport", "error", "getNotFoundViewport", "displayName", "metadata", "getResolvedMetadata", "resolveFinalMetadata", "result", "digest", "undefined", "metadataErr", "getNotFoundMetadata", "notFoundMetadataErr", "MetadataResolver", "div", "hidden", "fallback", "metadataState", "getMetadataReady", "getViewportReady", "StreamingMetadataOutletImpl", "promise", "StreamingMetadataOutlet", "getResolvedMetadataImpl", "errorConvention", "renderMetadata", "getNotFoundMetadataImpl", "notFoundErrorConvention", "getResolvedViewportImpl", "renderViewport", "getNotFoundViewportImpl", "resolvedMetadata", "elements", "createMetadataElements", "map", "el", "index", "key", "resolvedViewport", "createViewportElements", "alternates", "itunes", "facebook", "pinterest", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons"], "mappings": ";AAAA,OAAOA,SAASC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,QAAQ,QAAO;AAM5D,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,aAAa,QACR,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAEEC,eAAe,EACfC,eAAe,QACV,qBAAoB;AAC3B,SAASC,UAAU,QAAQ,kBAAiB;AAK5C,SAASC,yBAAyB,QAAQ,oEAAmE;AAG7G,SACEC,sBAAsB,EACtBC,sBAAsB,QACjB,uBAAsB;AAC7B,SAASC,mBAAmB,QAAQ,kDAAiD;AACrF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mCAAmC,QAAQ,qCAAoC;AACxF,SAASC,+BAA+B,QAAQ,gCAA+B;AAE/E,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,EAavB;IAOC,MAAMC,eAAed,oCACnBK,aACAK;IAEF,MAAMK,sBAAsBd,gCAC1BG,UACAM;IAGF,SAASM;QACP,qBACE;;8BACE,KAACJ;8BACC,cAAA,KAACK;;gBAGFT,uCACC,KAACU;oBAAKC,MAAK;oBAAmBC,SAAQ;qBACpC;;;IAGV;IAEA,SAASC;QACP,qBACE,KAACV;sBACC,cAAA,KAACW;;IAGP;IAEA,SAASC;QACP,OAAOC,oBACLrB,MACAW,cACAP,4BACAG,WACAD;IAEJ;IAEA,eAAeQ;QACb,IAAI;YACF,OAAO,MAAMM;QACf,EAAE,OAAOE,OAAO;YACd,IAAI,CAAChB,aAAad,0BAA0B8B,QAAQ;gBAClD,IAAI;oBACF,OAAO,MAAMC,oBACXvB,MACAW,cACAP,4BACAG;gBAEJ,EAAE,OAAM,CAAC;YACX;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;QACT;IACF;IACAO,SAASU,WAAW,GAAG9B;IAEvB,SAAS+B;QACP,OAAOC,oBACL1B,MACAY,qBACAD,cACAP,4BACAD,iBACAI,WACAD;IAEJ;IAEA,eAAeqB;QACb,IAAIC;QACJ,IAAIN,QAAQ;QACZ,IAAI;YACFM,SAAS,MAAMH;YACf,OAAO;gBACLA,UAAUG;gBACVN,OAAO;gBACPO,QAAQC;YACV;QACF,EAAE,OAAOC,aAAa;YACpBT,QAAQS;YACR,IAAI,CAACzB,aAAad,0BAA0BuC,cAAc;gBACxD,IAAI;oBACFH,SAAS,MAAMI,oBACbhC,MACAY,qBACAD,cACAP,4BACAD,iBACAI;oBAEF,OAAO;wBACLkB,UAAUG;wBACVN;wBACAO,MAAM,EAAGP,yBAAD,AAACA,MAAeO,MAAM;oBAChC;gBACF,EAAE,OAAOI,qBAAqB;oBAC5BX,QAAQW;oBACR,8DAA8D;oBAC9D,8EAA8E;oBAC9E,IAAIvB,0BAA0Bd,WAAWqC,sBAAsB;wBAC7D,MAAMA;oBACR;gBACF;YACF;YACA,8DAA8D;YAC9D,8EAA8E;YAC9E,IAAIvB,0BAA0Bd,WAAWmC,cAAc;gBACrD,MAAMA;YACR;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;gBACLN,UAAUG;gBACVN;gBACAO,MAAM,EAAGP,yBAAD,AAACA,MAAeO,MAAM;YAChC;QACF;IACF;IAEA,SAASV;QACP,IAAI,CAACT,wBAAwB;YAC3B,qBAAO,KAACwB;QACV;QACA,qBACE,KAACC;YAAIC,MAAM;sBACT,cAAA,KAAC/D;gBAASgE,UAAU;0BAClB,cAAA,KAACH;;;IAIT;IAEA,eAAeA;QACb,MAAMI,gBAAgB,MAAMX;QAC5B,OAAOW,cAAcb,QAAQ;IAC/B;IAEAN,SAASK,WAAW,GAAG/B;IAEvB,eAAe8C;QACb,4DAA4D;QAC5D,iEAAiE;QACjE,IAAI,CAAC7B,wBAAwB;YAC3B,MAAMe;QACR;QACA,OAAOK;IACT;IAEA,eAAeU;QACb,MAAMpB;QACN,OAAOU;IACT;IAEA,SAASW;QACP,qBAAO,KAAC9C;YAAoB+C,SAASf;;IACvC;IAEA,MAAMgB,0BAA0BjC,yBAC5B+B,8BACA;IAEJ,OAAO;QACL5B;QACAK;QACAsB;QACAD;QACAI;IACF;AACF;AAEA,MAAMjB,sBAAsBpD,MAAMsE;AAClC,eAAeA,wBACb5C,IAAgB,EAChBC,QAAyB,EACzBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB,EACpBD,SAA0C;IAE1C,MAAMuC,kBAAkBvC,cAAc,aAAawB,YAAYxB;IAC/D,OAAOwC,eACL9C,MACAC,UACAU,cACAP,4BACAD,iBACAI,WACAsC;AAEJ;AAEA,MAAMb,sBAAsB1D,MAAMyE;AAClC,eAAeA,wBACb/C,IAAgB,EAChBC,QAAyB,EACzBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB;IAEpB,MAAMyC,0BAA0B;IAChC,OAAOF,eACL9C,MACAC,UACAU,cACAP,4BACAD,iBACAI,WACAyC;AAEJ;AAEA,MAAM3B,sBAAsB/C,MAAM2E;AAClC,eAAeA,wBACbjD,IAAgB,EAChBW,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB,EACpBD,SAA0C;IAE1C,MAAMuC,kBAAkBvC,cAAc,aAAawB,YAAYxB;IAC/D,OAAO4C,eACLlD,MACAW,cACAP,4BACAG,WACAsC;AAEJ;AAEA,MAAMtB,sBAAsBjD,MAAM6E;AAClC,eAAeA,wBACbnD,IAAgB,EAChBW,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB;IAEpB,MAAMyC,0BAA0B;IAChC,OAAOE,eACLlD,MACAW,cACAP,4BACAG,WACAyC;AAEJ;AAEA,eAAeF,eACb9C,IAAgB,EAChBC,QAAyB,EACzBU,YAAqC,EACrCP,0BAAsD,EACtDD,eAAgC,EAChCI,SAAoB,EACpBsC,eAAmC;IAEnC,MAAMO,mBAAmB,MAAM/D,gBAC7BW,MACAC,UACAU,cACAkC,iBACAzC,4BACAG,WACAJ;IAEF,MAAMkD,WACJC,uBAAuBF;IACzB,qBACE;kBACGC,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOlF,aAAaiF,IAA0B;gBAAEE,KAAKD;YAAM;QAC7D;;AAGN;AAEA,eAAeP,eACblD,IAAgB,EAChBW,YAAqC,EACrCP,0BAAsD,EACtDG,SAAoB,EACpBsC,eAAmC;IAEnC,MAAMc,mBAAmB,MAAMrE,gBAC7BU,MACAW,cACAkC,iBACAzC,4BACAG;IAGF,MAAM8C,WACJO,uBAAuBD;IACzB,qBACE;kBACGN,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOlF,aAAaiF,IAA0B;gBAAEE,KAAKD;YAAM;QAC7D;;AAGN;AAEA,SAASH,uBAAuB7B,QAA0B;IACxD,OAAOlC,WAAW;QAChBZ,UAAU;YAAE8C;QAAS;QACrBzC,mBAAmB;YAAE6E,YAAYpC,SAASoC,UAAU;QAAC;QACrDnF,WAAW;YAAEoF,QAAQrC,SAASqC,MAAM;QAAC;QACrChF,aAAa;YAAEiF,UAAUtC,SAASsC,QAAQ;QAAC;QAC3ChF,cAAc;YAAEiF,WAAWvC,SAASuC,SAAS;QAAC;QAC9CvF,oBAAoB;YAAEwF,iBAAiBxC,SAASwC,eAAe;QAAC;QAChEpF,iBAAiB;YAAEqF,cAAczC,SAASyC,YAAY;QAAC;QACvD1F,gBAAgB;YAAE2F,aAAa1C,SAAS0C,WAAW;QAAC;QACpDlF,kBAAkB;YAAEmF,WAAW3C,SAAS2C,SAAS;QAAC;QAClDlF,gBAAgB;YAAEmF,SAAS5C,SAAS4C,OAAO;QAAC;QAC5ClF,aAAa;YAAEmF,UAAU7C,SAAS6C,QAAQ;QAAC;QAC3ClF,cAAc;YAAEmF,OAAO9C,SAAS8C,KAAK;QAAC;KACvC;AACH;AAEA,SAASX,uBAAuBxC,QAA0B;IACxD,OAAO7B,WAAW;QAACX,aAAa;YAAEwC,UAAUA;QAAS;KAAG;AAC1D", "ignoreList": [0]}