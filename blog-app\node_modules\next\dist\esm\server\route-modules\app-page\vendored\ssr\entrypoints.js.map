{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactDOM from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\nimport * as ReactDOMServer from 'react-dom/server'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg: 'react-server-dom-turbopack/client' | 'react-server-dom-webpack/client'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackClient, ReactServerDOMWebpackClient\nif (process.env.TURBOPACK) {\n  ReactServerDOMTurbopackClient =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/client') as typeof import('react-server-dom-turbopack/client')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackClient = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/client'\n    )\n  }\n} else {\n  ReactServerDOMWebpackClient =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client') as typeof import('react-server-dom-webpack/client')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackClient = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/client'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactDOMServer,\n  ReactServerDOMTurbopackClient,\n  ReactServerDOMWebpackClient,\n}\n"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactCompilerRuntime", "ReactDOMServer", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackClient", "ReactServerDOMWebpackClient", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,YAAW;AACrC,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AACpD,YAAYC,0BAA0B,yBAAwB;AAE9D,YAAYC,oBAAoB,mBAAkB;AAElD,SAASC,0BACPC,IAA6B,EAC7BC,GAA4E;IAE5E,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIS,+BAA+BC;AACnC,IAAId,QAAQC,GAAG,CAACc,SAAS,EAAE;IACzBF,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DG,QAAQ;IACV,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,8BAA8BjB,0BAC5B,aACA;IAEJ;AACF,OAAO;IACLiB,8BACE,6DAA6D;IAC7DE,QAAQ;IACV,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,gCAAgChB,0BAC9B,WACA;IAEJ;AACF;AAEA,SACEN,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfC,oBAAoB,EACpBH,QAAQ,EACRI,cAAc,EACdiB,6BAA6B,EAC7BC,2BAA2B,KAC5B", "ignoreList": [0]}